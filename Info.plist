<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>24D81</string>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh_CN</string>
	<key>CFBundleDisplayName</key>
	<string>光·遇多功能</string>
	<key>CFBundleExecutable</key>
	<string>Sky-iOS-Gold</string>
	<key>CFBundleIconFiles</key>
	<array>
		<string>icon.png</string>
		<string><EMAIL></string>
		<string><EMAIL></string>
	</array>
	<key>CFBundleIdentifier</key>
	<string>com.netease.sky2</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>光·遇多功能</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>0.14.3</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>iPhoneOS</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sky-dev</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>bilibili</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bili1c0f878631734f60</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Godlike</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>glma75ef14a716a4</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>aecfs5n2y4aaaakd-g-ma75</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.netease.mpay.2jfepsztqu7vc0fg</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxa140d866ec0fe092</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb25327105</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>DouYin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>aw73k3do3bcel2lf</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>xiaohongshu</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>xhsad5b6e7070170df65923f283833a4459</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>kuaishou</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ks680606497107883305</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>datalink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dygame240021</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>326128</string>
	<key>DTAppStoreToolsBuild</key>
	<string>16F3</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>22C146</string>
	<key>DTPlatformName</key>
	<string>iphoneos</string>
	<key>DTPlatformVersion</key>
	<string>18.2</string>
	<key>DTSDKBuild</key>
	<string>22C146</string>
	<key>DTSDKName</key>
	<string>iphoneos18.2</string>
	<key>DTXcode</key>
	<string>1620</string>
	<key>DTXcodeBuild</key>
	<string>16C5032a</string>
	<key>DumpDecrypter Tool</key>
	<string>from DumpDecrypter Tool</string>
	<key>Environment</key>
	<string>obt</string>
	<key>GCSupportedGameControllers</key>
	<array>
		<dict>
			<key>ProfileName</key>
			<string>ExtendedGamepad</string>
		</dict>
	</array>
	<key>GCSupportsControllerUserInteraction</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>ITSDRMScheme</key>
	<string>v2</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>bilibili</string>
		<string>bilibili.following</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqzone</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV4</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqwpa</string>
		<string>itms-beta</string>
		<string>mqqopensdkdataline</string>
		<string>mqq</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqapi</string>
		<string>weixin</string>
		<string>neteasegl</string>
		<string>glopenapi</string>
		<string>weixinULAPI</string>
		<string>weibosdk</string>
		<string>weibosdk2.5</string>
		<string>weibosdk3.3</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>yixin</string>
		<string>douyinsharesdk</string>
		<string>douyinopensdk</string>
		<string>snssdk1128</string>
		<string>xhsdiscover</string>
		<string>kwai</string>
		<string>kwaiopenapi</string>
		<string>kwai.clip.multi</string>
		<string>KwaiSDKMediaV2</string>
		<string>gifshow</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<real>10</real>
	<key>NFCReaderUsageDescription</key>
	<string>For scanning Sky STAR products</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>For scanning invitation tokens and others.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>正在申请麦克风录音权限</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>为了保存您的截屏</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>为了保存和读取您的截屏</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Sky optionally requires speech recognition access to convert your speech to text.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>您的数据将仅被用于提高您的游戏体验，您可以随时在设置中停止该授权</string>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>SkyAPNSProduction</key>
	<true/>
	<key>SkyBranchName</key>
	<string>Main</string>
	<key>SkyBuildAccessKey</key>
	<string>1743442389-****************************************************************</string>
	<key>SkyConfigDomain</key>
	<string>https://ma75.update.netease.com/config</string>
	<key>SkyDevGameLibrarySymbolsName</key>
	<string>Game.dylib</string>
	<key>SkyDevelopmentTeam</key>
	<string>V6FGEP98X5</string>
	<key>SkyEnableODR</key>
	<string>false</string>
	<key>SkyServerHostname</key>
	<string>dev.radiance.thatgamecompany.com</string>
	<key>SkyStageName</key>
	<string>Dev</string>
	<key>UIAppFonts</key>
	<array>
		<string>Lato-Black.ttf</string>
		<string>Lato-BlackItalic.ttf</string>
		<string>Lato-Bold.ttf</string>
		<string>Lato-BoldItalic.ttf</string>
		<string>Lato-Hairline.ttf</string>
		<string>Lato-HairlineItalic.ttf</string>
		<string>Lato-Light.ttf</string>
		<string>Lato-LightItalic.ttf</string>
		<string>Lato-Italic.ttf</string>
		<string>Lato-Regular.ttf</string>
		<string>MarugameHonmaruGothic-R.otf</string>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UILaunchToFullScreenByDefaultOnMac</key>
	<true/>
	<key>UIPrerenderedIcon</key>
	<true/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
		<string>metal</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>UISupportsTrueScreenSizeOnMac</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>appGroupId</key>
	<string>group.com.netease.sky</string>
</dict>
</plist>
