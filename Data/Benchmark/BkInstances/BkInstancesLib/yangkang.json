{"username": "yangkang", "usecase_instances": [{"m_name": "Dawn", "m_params": {"level_name": "Dawn", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "CandleSpaceEnd", "m_params": {"level_name": "CandleSpaceEnd", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "DayEnd", "m_params": {"level_name": "DayEnd", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Dusk", "m_params": {"level_name": "Dusk", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Night", "m_params": {"level_name": "Night", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Night_ValleyForest", "m_params": {"level_name": "Night_ValleyForest", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Prairie_Island", "m_params": {"level_name": "Prairie_Island", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Prairie_Village", "m_params": {"level_name": "Prairie_Village", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Prairie_WildLifePark", "m_params": {"level_name": "Prairie_WildLifePark", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "RainShelter", "m_params": {"level_name": "RainShelter", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Sunset", "m_params": {"level_name": "Sunset", "event_id": 0, "event_name": "", "record_level_track": false}, "m_type": "LevelTraveler", "m_auto_test": true}, {"m_name": "Outfit-test-1", "m_params": {"name": "CharSkyKid_Body_AP11Ancestor_01"}, "m_type": "Outfit", "m_auto_test": false}]}