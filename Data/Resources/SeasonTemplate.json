[{"key": "current_min_season", "season": "default", "template_value": "2"}, {"key": "current_max_season", "season": "default", "template_value": "26"}, {"key": "number", "season": "1", "template_value": "00"}, {"key": "number", "season": "2", "template_value": "02"}, {"key": "number", "season": "3", "template_value": "03"}, {"key": "number", "season": "4", "template_value": "04"}, {"key": "number", "season": "5", "template_value": "05"}, {"key": "number", "season": "6", "template_value": "06"}, {"key": "number", "season": "7", "template_value": "07"}, {"key": "number", "season": "8", "template_value": "08"}, {"key": "number", "season": "9", "template_value": "09"}, {"key": "number", "season": "default", "template_value": "{{season}}"}, {"key": "name", "season": "default", "template_value": "season_{{number}}"}, {"key": "pendant_name", "season": "default", "template_value": "season_pass_info_item_pendant_{{number}}"}, {"key": "spirit_name", "season": "default", "template_value": "name_spirit_season_{{number}}"}, {"key": "finish_unlock", "season": "default", "template_value": "season_finish_{{number}}"}, {"key": "completed_unlock", "season": "default", "template_value": "season_completed_all_{{number}}"}, {"key": "pass_unlock", "season": "default", "template_value": "season_pass_{{number}}"}, {"key": "bonus_unlock", "season": "default", "template_value": "season_pass_bonus_{{number}}"}, {"key": "pack_unlock", "season": "default", "template_value": "season_pass_pack3_{{number}}"}, {"key": "preorder_unlock", "season": "default", "template_value": "season_pass_{{number}}_preorder"}, {"key": "pendant_unlock", "season": "1", "template_value": "CharSky<PERSON>id_Neck_AP01Pendant"}, {"key": "pendant_unlock", "season": "5", "template_value": "CharSky<PERSON>id_Neck_AP01Pendant"}, {"key": "pendant_unlock", "season": "default", "template_value": "CharSky<PERSON>id_Neck_AP{{number}}Pendant"}, {"key": "intro_unlock", "season": "1", "template_value": "APUnlock"}, {"key": "intro_unlock", "season": "default", "template_value": "AP{{season}}Unlock"}, {"key": "intro_unlock_2", "season": "7", "template_value": "Ap07IntroPrairieIsland"}, {"key": "intro_unlock_2", "season": "8", "template_value": "Ap08IntroDawnCave"}, {"key": "intro_unlock_2", "season": "9", "template_value": "Ap09Intro_01"}, {"key": "intro_unlock_2", "season": "default", "template_value": "AP{{season}}Intro_01"}, {"key": "constellation", "season": "1", "template_value": "name_season_01"}, {"key": "constellation", "season": "default", "template_value": "name_season_{{number}}"}, {"key": "quest_giver_npc", "season": "1", "template_value": "questap01"}, {"key": "quest_giver_npc", "season": "default", "template_value": "questap{{number}}"}, {"key": "icon_name", "season": "1", "template_value": "UiOutfitPendantAP05"}, {"key": "icon_name", "season": "default", "template_value": "UiOutfitPendantAP{{number}}"}, {"key": "icon_name_fallback", "season": "default", "template_value": "UiOutfitPendant"}, {"key": "candle_mesh_name", "season": "default", "template_value": "SeasonCandle{{number}}"}, {"key": "candle_mesh_name_fallback", "season": "default", "template_value": "SeasonCandle"}, {"key": "candle_icon_name", "season": "5", "template_value": "UiMenuSeasonCandleAP01"}, {"key": "candle_icon_name", "season": "default", "template_value": "UiMenuSeasonCandleAP{{number}}"}, {"key": "candle_icon_name_fallback", "season": "default", "template_value": "UiMenuSeasonCandle"}, {"key": "heart_icon_name", "season": "5", "template_value": "UiMenuSeasonHeartAP01"}, {"key": "heart_icon_name", "season": "default", "template_value": "UiMenuSeasonHeartAP{{number}}"}, {"key": "heart_icon_name_fallback", "season": "default", "template_value": "UiMenuSeasonHeart"}, {"key": "gift_icon_name", "season": "5", "template_value": "UiMenuSeasonGiftAP01"}, {"key": "gift_icon_name", "season": "default", "template_value": "UiMenuSeasonGiftAP{{number}}"}, {"key": "gift_icon_name_fallback", "season": "default", "template_value": "UiSocialSeasonGift"}, {"key": "constellation_image_name", "season": "default", "template_value": "ConstellationImageAP{{number}}"}, {"key": "constellation_image_name_fallback", "season": "default", "template_value": "ConstellationImageAP"}, {"key": "pass_promo", "season": "default", "template_value": "UISeasonPassPromoAP{{number}}"}, {"key": "pass_promo_fallback", "season": "default", "template_value": "UISeasonPassPromoAP"}, {"key": "pass_promo_2", "season": "default", "template_value": "UISeasonPassPromo2AP{{number}}"}, {"key": "pass_promo_2_fallback", "season": "default", "template_value": "UISeasonPassPromo2AP"}, {"key": "pass_promo_3", "season": "default", "template_value": "UISeasonPassPromo3AP{{number}}"}, {"key": "pass_promo_3_fallback", "season": "default", "template_value": "UISeasonPassPromo3AP"}, {"key": "pass_promo_4", "season": "default", "template_value": "UISeasonPassPromo4AP{{number}}"}, {"key": "pass_promo_4_fallback", "season": "default", "template_value": "UISeasonPassPromo4AP"}, {"key": "pass_promo_5", "season": "default", "template_value": "UISeasonPassPromo5AP{{number}}"}, {"key": "pass_promo_5_fallback", "season": "default", "template_value": "UISeasonPassPromo5AP"}, {"key": "pass_preorder_promo", "season": "default", "template_value": "UISeasonPassPreoderPromoAP{{number}}"}, {"key": "pass_preorder_promo_fallback", "season": "default", "template_value": "UISeasonPassPreoderPromo"}, {"key": "iap_icon_SPASSR", "season": "default", "template_value": "{{pass_promo_2}}"}, {"key": "iap_icon_SPASSB", "season": "default", "template_value": "{{pass_promo_2}}"}, {"key": "iap_icon_SPASSQR", "season": "default", "template_value": "{{icon_name}}"}, {"key": "iap_icon_SPASSP3", "season": "default", "template_value": "{{pass_promo_3}}"}, {"key": "iap_icon_SPASSQP3", "season": "default", "template_value": "{{icon_name}}"}, {"key": "iap_icon_SCDL5", "season": "default", "template_value": "{{candle_icon_name}}"}, {"key": "iap_icon_SPASSG", "season": "default", "template_value": "{{gift_icon_name}}"}, {"key": "iap_unlocks_SPASSR", "season": "default", "template_value": "{{pass_unlock}},{{pendant_unlock}}"}, {"key": "iap_unlocks_SPASSB", "season": "default", "template_value": "{{pass_unlock}},{{pendant_unlock}},{{bonus_unlock}}"}, {"key": "iap_unlocks_SPASSQR", "season": "default", "template_value": "{{pass_unlock}},{{pendant_unlock}},{{preorder_unlock}}"}, {"key": "iap_unlocks_SPASSP3", "season": "default", "template_value": "{{pass_unlock}},{{pendant_unlock}},{{pack_unlock}}"}, {"key": "iap_unlocks_SPASSQP3", "season": "default", "template_value": "{{pass_unlock}},{{pendant_unlock}},{{pack_unlock}},{{preorder_unlock}}"}, {"key": "season_15", "season": "default", "template_value": "破晓季"}, {"key": "season_16", "season": "default", "template_value": "欧若拉季"}, {"key": "season_17", "season": "default", "template_value": "追忆季"}, {"key": "season_18", "season": "default", "template_value": "夜行季"}, {"key": "season_19", "season": "default", "template_value": "拾光季"}, {"key": "season_20", "season": "default", "template_value": "归巢季"}, {"key": "season_21", "season": "default", "template_value": "九色鹿季"}, {"key": "season_22", "season": "default", "template_value": "筑巢季"}, {"key": "season_23", "season": "default", "template_value": "二重奏季"}, {"key": "season_24", "season": "default", "template_value": "姆明季"}, {"key": "season_25", "season": "default", "template_value": "彩染季"}, {"key": "season_26", "season": "default", "template_value": "青鸟季"}]