[{"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap09_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap09_fetch_01", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap09_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap09_fetch_02", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap09_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap09_fetch_03", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap09_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap09_fetch_04", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Bookshelf", "id": "ap10_fetch_01", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Torch", "id": "ap10_fetch_02", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Tent", "id": "ap10_fetch_03", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Spotlight", "id": "ap10_fetch_04", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Hammock", "id": "ap10_fetch_05", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap10_fetch_06_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitPropAP10Hoop", "id": "ap10_fetch_06", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap11_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap11_fetch_04", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap12_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap12_fetch_01", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap12_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap12_fetch_02", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap12_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap12_fetch_03", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap12_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap12_fetch_04", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap12_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap12_fetch_05", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap13_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap13_fetch_01", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap13_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap13_fetch_02", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap13_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap13_fetch_03", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap13_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap13_fetch_04", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap13_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap13_fetch_05", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_01", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_02", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_03", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_04", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_05", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap14_fetch_rec_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap14_fetch_rec", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap16_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap16_fetch_01", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap16_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap16_fetch_02", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap16_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap16_fetch_03", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap16_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap16_fetch_04", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap17_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap17_fetch_01", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap17_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap17_fetch_03", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap17_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap17_fetch_05", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap17_fetch_07_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap17_fetch_07", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap17_fetch_09_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap17_fetch_09", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap18_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap18_fetch_01", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap18_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap18_fetch_02", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap18_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap18_fetch_03", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap18_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap18_fetch_04", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap18_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap18_fetch_05", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap19_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap19_fetch_01", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap19_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap19_fetch_02", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap19_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap19_fetch_03", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap19_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap19_fetch_04", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap19_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap19_fetch_05", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_10_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_10", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_01", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_02", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_03", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_04", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_05", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap21_fetch_05_part02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap21_fetch_05_part02", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_fetch_01", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_fetch_02", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_fetch_03", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_fetch_04", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_fetch_05", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniHouse_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniHouse_01", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniHouse_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniHouse_02", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniHouse_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniHouse_03", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniShop_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniShop_01", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniShop_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniShop_02", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniShop_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniShop_03", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniSocial_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniSocial_01", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniSocial_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniSocial_02", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap22_miniSocial_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap22_miniSocial_03", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap23_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap23_fetch_01", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap23_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap23_fetch_02", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap23_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap23_fetch_03", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap23_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap23_fetch_04", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap23_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap23_fetch_05", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_01", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_03", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_05", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_07_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_07", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_09_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_09", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap24_fetch_11_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap24_fetch_11", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap25_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap25_fetch_01", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap25_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap25_fetch_02", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap25_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap25_fetch_03", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap25_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap25_fetch_04", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap25_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap25_fetch_05", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_01", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_02", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_03", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_04", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_05", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap26_fetch_06_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap26_fetch_06", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_01", "level_name": "DuskMid_PastMarket", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_02", "level_name": "DuskMid_PastMarket", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_03", "level_name": "DuskMid_Past", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_04", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_05", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_fetch_06_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_fetch_06", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_manateeTrust_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_manateeTrust_01", "level_name": "DuskMid_Past", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_manateeTrust_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_manateeTrust_02", "level_name": "DuskMid_Past", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_manateeTrust_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_manateeTrust_03", "level_name": "DuskMid_Past", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_manateeTrust_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_manateeTrust_04", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap27_manateeTrust_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap27_manateeTrust_05", "level_name": "MainStreet_ShopProps", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "barbershop_activated_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "barbershop_activated", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuCamera", "id": "cafe_photobooth", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "UiOutfitHornPastryRoll", "id": "cafe_serving", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "capeshop_activated_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "capeshop_activated", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "competition_cloudcollect", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "competition_speedskate", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "competition_voidcollect", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06un<PERSON>_cs_dol_friendship_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "cs_dol_friendship", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "balance", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s8"], "event_reward_amount": 100, "icon": "", "id": "dawn_balance", "level_name": "DawnCave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "carry", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 50, "icon": "", "id": "dawn_carry", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "chestpound", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s8"], "event_reward_amount": 100, "icon": "", "id": "dawn_chestpound", "level_name": "DawnCave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "come", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_come", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "deepbreath", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s8"], "event_reward_amount": 100, "icon": "", "id": "dawn_deepbreath", "level_name": "DawnCave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "dustoff", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s8"], "event_reward_amount": 100, "icon": "", "id": "dawn_dustoff", "level_name": "DawnCave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "dawn_first_wingbuff", "level_name": "Dawn", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "hackysack", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_hackysack", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "loopdance", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 50, "icon": "", "id": "dawn_loopdance", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "mope", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_mope", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "nothanks", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_nothanks", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "point", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_point", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "pullup", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_pullup", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "roll", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dawn_roll", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sass", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 100, "icon": "", "id": "dawn_sass", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "dawn_temple", "level_name": "Dawn", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 100, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "welcome", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 50, "icon": "", "id": "dawn_welcome", "level_name": "Dawn", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "acknowledge", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 50, "icon": "", "id": "dusk_acknowledge", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "anxious", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s13"], "event_reward_amount": 50, "icon": "", "id": "dusk_anxious", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "brave", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_brave", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 5, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "dusk_crabfield_flippedCrab_02", "level_name": "Dusk_CrabField", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 5, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "crabvoice", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 100, "icon": "", "id": "dusk_crabvoice", "level_name": "Dusk_CrabField", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "die", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_die", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "dontgo", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 2, "icon": "", "id": "dusk_dontgo", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "<PERSON><PERSON>gh", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s13"], "event_reward_amount": 50, "icon": "", "id": "dusk_evillaugh", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 4, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "dusk_graveyard_flippedCrab_01", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 4, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "lookaround", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_lookaround", "level_name": "DuskMid", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "dusk_oasis_basicCrab_03", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ouch", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s13"], "event_reward_amount": 6, "icon": "", "id": "dusk_ouch", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "respect", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 50, "icon": "", "id": "dusk_respect", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "salute", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_salute", "level_name": "Dusk_CrabField", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "scared", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_scared", "level_name": "Dusk", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sneaky", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "dusk_sneaky", "level_name": "DuskGraveyard", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "wait", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s13"], "event_reward_amount": 6, "icon": "", "id": "dusk_wait", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "questap23_duo", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "mainStreet_doubleBow", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "questap23_duo_cellist", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "mainStreet_doubleBow2", "level_name": "MainStreet_ConcertHall", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "cartwheel", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "mainstreet_cartwheel", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "hearthands", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "mainstreet_hearthands", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "hypedance", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "mainstreet_hypedance", "level_name": "MainStreet_ShopOutfits", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "maskshop_activated_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "maskshop_activated", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_nature_vortex1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "nature_vortex1", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_nature_vortex2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "nature_vortex2", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_nature_vortex3_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "nature_vortex3", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "beckon", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 6, "icon": "", "id": "night_beckon", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "night_desertbeach_hoveringJellyFish_03", "level_name": "NightDesert_Beach", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "float", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_float", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "force", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_force", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ghost", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_ghost", "level_name": "Night2", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "gloat", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 50, "icon": "", "id": "night_gloat", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "grief", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s17"], "event_reward_amount": 50, "icon": "", "id": "night_grief", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "injured", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s17"], "event_reward_amount": 50, "icon": "", "id": "night_injured", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "kungfu", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 6, "icon": "", "id": "night_kungfu", "level_name": "Night2", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "love", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_love", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "plead", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s17"], "event_reward_amount": 50, "icon": "", "id": "night_plead", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "pray", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_pray", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "scheme", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 50, "icon": "", "id": "night_scheme", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "shh", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 6, "icon": "", "id": "night_shh", "level_name": "Night2", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "slouch", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 50, "icon": "", "id": "night_slouch", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sneeze", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 50, "icon": "", "id": "night_sneeze", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "stretch", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s11"], "event_reward_amount": 50, "icon": "", "id": "night_stretch", "level_name": "NightDesert", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "think", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 6, "icon": "", "id": "night_think", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "tiptoe", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s17"], "event_reward_amount": 50, "icon": "", "id": "night_tiptoe", "level_name": "Night_Shelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "wise", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 6, "icon": "", "id": "night_wise", "level_name": "Night", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "crabwalk", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 50, "icon": "", "id": "oasis_crabwalk", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "doze", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 100, "icon": "", "id": "oasis_doze", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "nod", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 2, "icon": "", "id": "oasis_nod", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "playfight", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 100, "icon": "", "id": "oasis_playfight", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "scare", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 50, "icon": "", "id": "oasis_scare", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "shrug", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s6"], "event_reward_amount": 6, "icon": "", "id": "oasis_shrug", "level_name": "DuskOasis", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "bellyscratch", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 2, "icon": "", "id": "prairie_bellyscratch", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "bird", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_bird", "level_name": "Prairie_NestAndKeeper", "localization_key": "the_bird_watcher", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "butterfly", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_butterfly", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "the_butterfly_keeper", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 4, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "prairie_cave_basicCrab_01", "level_name": "Prairie_Cave", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 4, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "celebrate", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 50, "icon": "", "id": "prairie_celebrate", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "dance", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 50, "icon": "", "id": "prairie_dance", "level_name": "Prairie_NestAndKeeper", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "doublefive", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 50, "icon": "", "id": "prairie_doublefive", "level_name": "Prairie_NestAndKeeper", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "gratitude", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 50, "icon": "", "id": "prairie_gratitude", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "grumpy", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 6, "icon": "", "id": "prairie_grumpy", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "prairie_island_hoveringJellyFish_03", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "jelly", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 50, "icon": "", "id": "prairie_jelly", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "jollydance", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_jollydance", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "laugh", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_laugh", "level_name": "Prairie_Village", "localization_key": "a_funny_chore", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 5, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "prairie_nestkeeper_flyingManta_02", "level_name": "Prairie_NestAndKeeper", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 5, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "nightbird", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_nightbird", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "rally", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 50, "icon": "", "id": "prairie_rally", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "salutation", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 2, "icon": "", "id": "prairie_salutation", "level_name": "Prairie_Cave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sidehug", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_sidehug", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "thumbsup", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_thumbsup", "level_name": "Prairie_Village", "localization_key": "good_job_friend", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "timid", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s7"], "event_reward_amount": 100, "icon": "", "id": "prairie_timid", "level_name": "Prairie_Island", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "wave", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_wave", "level_name": "Prairie_Village", "localization_key": "waving_at_work", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "windpose", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_windpose", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "wipe", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_wipe", "level_name": "Prairie_Cave", "localization_key": "hard_work", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "yawn", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "prairie_yawn", "level_name": "Prairie_Village", "localization_key": "well_earned_rest", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "wipe", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 50, "icon": "", "id": "prefab_quest_test", "level_name": "PrefabQuestTest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "rain_basecamp_darkplantTrapped_02", "level_name": "Rain_BaseCamp", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "bubbles", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 100, "icon": "", "id": "rain_bubbles", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "chuckle", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 50, "icon": "", "id": "rain_chuckle", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "cold", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_cold", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "cry", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_cry", "level_name": "RainMid", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 5, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "rain_end_darkplantTrapped_01", "level_name": "RainEnd", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 5, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "eww", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 50, "icon": "", "id": "rain_eww", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "facepalm", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 50, "icon": "", "id": "rain_facepalm", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "hairtousle", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 50, "icon": "", "id": "rain_hairtousle", "level_name": "Rain_Cave", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "kiss", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 100, "icon": "", "id": "rain_kiss", "level_name": "RainMid", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "lazycool", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 2, "icon": "", "id": "rain_lazycool", "level_name": "RainForest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "marching", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 50, "icon": "", "id": "rain_marching", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ohno", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_ohno", "level_name": "RainMid", "localization_key": "oh_no", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "pout", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_pout", "level_name": "RainForest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sarcastic", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 50, "icon": "", "id": "rain_sarcastic", "level_name": "RainForest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "seek", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_seek", "level_name": "RainForest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "rain_shelter_hoveringJellyFish_03", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "shy", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_shy", "level_name": "RainForest", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sorry", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_sorry", "level_name": "RainMid", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "tsktsk", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s10"], "event_reward_amount": 100, "icon": "", "id": "rain_tsktsk", "level_name": "Rain", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "whale", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "rain_whale", "level_name": "RainMid", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuExploreMask", "id": "scavenger_spirit_beachday1", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuExploreMask", "id": "scavenger_spirit_beachday2", "level_name": "Dusk_Triangle", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuExploreMask", "id": "scavenger_spirit_rockcollection1", "level_name": "Prairie_WildLifePark", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "babymanta", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s12"], "event_reward_amount": 50, "icon": "", "id": "skyway_babymanta", "level_name": "Skyway", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "nerdy", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s12"], "event_reward_amount": 100, "icon": "", "id": "skyway_nerdy", "level_name": "Skyway", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "pointup", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s12"], "event_reward_amount": 50, "icon": "", "id": "skyway_pointup", "level_name": "Skyway", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "voila", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s12"], "event_reward_amount": 50, "icon": "", "id": "skyway_voila", "level_name": "Skyway", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuExploreMask", "id": "spirit_dailies_scavenger", "level_name": "", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "awww", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s14"], "event_reward_amount": 100, "icon": "", "id": "sunset_awww", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "backflip", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_backflip", "level_name": "SunsetColosseum", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "bow", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_bow", "level_name": "SunsetColosseum", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "cheer", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_cheer", "level_name": "SunsetColosseum", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "duetdance", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s14"], "event_reward_amount": 100, "icon": "", "id": "sunset_duetdance", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 60, "bonus_currency_type": "wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuCollectMask", "id": "sunset_flyrace", "level_name": "Sunset_FlyRace", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "handshake", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s14"], "event_reward_amount": 6, "icon": "", "id": "sunset_handshake", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "handstand", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_handstand", "level_name": "Sunset_Citadel", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "headbob", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s14"], "event_reward_amount": 50, "icon": "", "id": "sunset_headbob", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "joy", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s2"], "event_reward_amount": 100, "icon": "", "id": "sunset_joy", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "juggle", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s5"], "event_reward_amount": 2, "icon": "", "id": "sunset_juggle", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "manta", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_manta", "level_name": "Sunset_Citadel", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "proud", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_proud", "level_name": "SunsetColosseum", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 60, "bonus_currency_type": "wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuCollectMask", "id": "sunset_race", "level_name": "SunsetRace", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "sparkler", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s4"], "event_reward_amount": 100, "icon": "", "id": "sunset_sparkler", "level_name": "SunsetEnd2", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "strong", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "sunset_strong", "level_name": "Sunset", "localization_key": "a_proud_father", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "tripleaxel", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s3"], "event_reward_amount": 50, "icon": "", "id": "sunset_tripleaxel", "level_name": "Sunset", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "bearhug", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_s9"], "event_reward_amount": 50, "icon": "", "id": "sunset_village_bearhug", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 5, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "sunset_village_flyingManta_02", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 5, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "peek", "event_currency_type": "prestige_wax", "event_names": ["spirit_revisit_bonus_s9"], "event_reward_amount": 2, "icon": "", "id": "sunset_village_peek", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "showdance", "event_currency_type": "season_wax", "event_names": ["spirit_revisit_bonus_s9"], "event_reward_amount": 6, "icon": "", "id": "sunset_village_showdance", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "spintrick", "event_currency_type": "heart_wax", "event_names": ["spirit_revisit_bonus_s9"], "event_reward_amount": 100, "icon": "", "id": "sunset_village_spintrick", "level_name": "SunsetVillage", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 150, "bonus_currency_type": "wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiMenuCollectMask", "id": "sunset_yeti_race", "level_name": "Sunset_YetiPark", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 150, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 7, "bonus_currency_type": "prestige_wax", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "sunset_yetipark_flyingManta_03", "level_name": "Sunset_YetiPark", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 7, "reward_amount_2": 0, "reward_currency_type_1": "prestige_wax", "reward_currency_type_2": "", "type": "storm_spawner"}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "test_multilevel_race1", "level_name": "", "localization_key": "", "on_until_first_done": true, "once_lifetime": false, "reward_amount_1": 1, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_aaron", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_atlas", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_don", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_jenova", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_scott", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_skidmore", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_travis", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_vince", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "tgc_office_yui", "level_name": "TGCOffice", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "wax", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "washed_ashore", "level_name": "Prairie_Tutorial", "localization_key": "", "on_until_first_done": true, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "candles", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_qixi_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "UiSocialHug", "id": "qixi_fetch_01", "level_name": "RainShelter", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_01_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_01", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_02_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_02", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_03_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_03", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_04_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_04", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_05_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_05", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_06_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_06", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_07_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_07", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_08_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_08", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "ap06unlock_ap20_fetch_09_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "ap20_fetch_09", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_dawn_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_dawn_1", "level_name": "Dawn", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_dawn_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_dawn_2", "level_name": "Dawn", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_dusk_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_dusk_1", "level_name": "DuskStart", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_dusk_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_dusk_2", "level_name": "DuskStart", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_night_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_night_1", "level_name": "Night", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_night_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_night_2", "level_name": "Night", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_prairie_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_prairie_1", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_prairie_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_prairie_2", "level_name": "<PERSON><PERSON><PERSON>F<PERSON>s", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_rain_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_rain_1", "level_name": "Rain", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_rain_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_rain_2", "level_name": "Rain", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_storm_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_storm_1", "level_name": "StormStart", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_storm_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_storm_2", "level_name": "StormStart", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_sunset_1_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_sunset_1", "level_name": "Sunset", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "fireworks_sunset_2_quest_done", "event_currency_type": "wax", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "fireworks_sunset_2", "level_name": "Sunset", "localization_key": "", "on_until_first_done": false, "once_lifetime": false, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "pantsshop_activated_quest_done", "event_currency_type": "", "event_names": [], "event_reward_amount": 0, "icon": "", "id": "pantsshop_activated", "level_name": "CandleSpace", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "whistle", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_whistle", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "<PERSON><PERSON><PERSON>", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_princess<PERSON>ry", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "muscle", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_muscle", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}, {"bonus_amount": 0, "bonus_currency_type": "", "collectible_name": "floatdance", "event_currency_type": "wax", "event_names": ["spirit_revisit_bonus_wax"], "event_reward_amount": 0, "icon": "", "id": "night_floatdance", "level_name": "Night_PaintedWorld", "localization_key": "", "on_until_first_done": true, "once_lifetime": true, "reward_amount_1": 0, "reward_amount_2": 0, "reward_currency_type_1": "season_candle", "reward_currency_type_2": "", "type": ""}]