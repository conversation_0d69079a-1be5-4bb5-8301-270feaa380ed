[{"allow_roll": true, "allow_scale": false, "audio_material": 0, "category": "MiscellaneousSmall", "code_type_name": "Pinwheel", "face_away": false, "forward_offset": 2.0, "hang_offset": 0.0, "has_collision": false, "held_hand_pos": [], "max_place_distance": 15.0, "mesh": "Prop_Pinwheel_Red", "name": "Pinwheel_Red", "outfit_name": "CharSkyKid_Prop_Pinwheel_Red", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CharRampEvents"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(0.0,0.0,0.0,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 4, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 4, "volume": 4, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "audio_material": 0, "base_rgba": [1.0, 1.0, 1.0, 1.0], "category": "Special", "code_type_name": "KizunaOrnament", "diffuse1Tex": "CharRampKizunaAi", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "face_away": false, "forward_offset": 2.0, "hang_offset": 0.0, "has_collision": false, "held_hand_pos": [], "max_place_distance": 15.0, "mesh": "Prop_KizunaAi", "name": "KizunaOrnament", "normTex": "UpNormal", "normal_scale": 1.0, "outfit_name": "CharSkyKid_Prop_KizunaOrnament", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.05], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "selfLit": [1.0, 0.0, 1.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CharRampKizunaAi"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(1.0,0.0,1.0,1.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 20, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 20, "volume": 8, "volume_type": "S(Pot)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "audio_material": 0, "category": "<PERSON><PERSON><PERSON>", "code_type_name": "KizunaAiTVPillow", "drop_shadow_radius": 0.0, "face_away": false, "forward_offset": 2.0, "hang_offset": 0.0, "has_collision": true, "held_hand_pos": [3.193, 0.0, 1.132], "max_place_distance": 15.0, "mesh": "Prop_KizunaAiTVBorder", "name": "KizunaAiTVPillow", "outfit_name": "CharSkyKid_Prop_KizunaAiTVPillow", "recordable": false, "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "requires_water": false, "scale": [0.08], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "shader": "FlipBook", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "KizunaAiEmote"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(1.0,1.0,1.0,1.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "volume": 15, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "audio_material": 0, "category": "MiscellaneousSmall", "code_type_name": "Sunflower", "drop_shadow_radius": 0.0, "face_away": false, "forward_offset": 2.0, "hang_offset": 0.0, "has_collision": true, "held_hand_pos": [], "max_place_distance": 15.0, "mesh": "CharSkyKid_Prop_Sunflower", "name": "Sunflower", "outfit_name": "CharSkyKid_Prop_Sunflower", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.15], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CharRampEvents2"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(0.0,0.0,0.0,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "volume": 5, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "audio_material": 0, "category": "MiscellaneousSmall", "code_type_name": "Placeable", "drop_shadow_radius": 0.0, "face_away": false, "forward_offset": 2.0, "hang_offset": 1.1, "has_collision": true, "held_hand_pos": [], "max_place_distance": 15.0, "mesh": "Prop_QixiBird", "name": "QixiBird", "outfit_name": "CharSkyKid_Prop_QixiBird", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.12], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CharRampLNY"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(0.0,0.0,0.0,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "volume": 5, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribute_override": [], "attribute_override_amount": 0, "attribTex": "", "audio_material": 52, "candle_params": [], "category": "Furniture", "code_type_name": "CloudMusicTable", "diffuse1_scale": 1.0, "diffuse1Tex": "White", "diffuse2Tex": "White", "base_rgba": [0.125, 0.03125, 0.0, 1.0], "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(-14.51, -1, 0)", "rotation": "(-90,0,0)", "scale": "(1.07,1.07,1.07)"}, {"offset": "(-4.91, -1, 8.75)", "rotation": "(0,0,0)", "scale": "(1.07,1.07,1.07)"}, {"offset": "(4.91, -1, 8.75)", "rotation": "(0,0,0)", "scale": "(1.07,1.07,1.07)"}, {"offset": "(14.51, -1, 0)", "rotation": "(90,0,0)", "scale": "(1.07,1.07,1.07)"}], "max_place_distance": 15.0, "mesh": "Prop_CloudMusicTable", "name": "CloudMusicTable", "normal_scale": 1.0, "normTex": "UpNormal", "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSit2", "chat_enabled": true}, "outfit_name": "CharSkyKid_Prop_CloudMusicTable", "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.075], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CharRampXmas_Tex"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "StoneBase"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(0.0,0.0,0.0,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "volume": 30, "volume_type": "XL(Table)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "audio_material": 0, "category": "MiscellaneousSmall", "code_type_name": "Placeable", "drop_shadow_radius": 0.0, "face_away": false, "forward_offset": 2.0, "hang_offset": 0.0, "has_collision": false, "held_hand_pos": [], "max_place_distance": 15.0, "mesh": "Prop_BalloonRed", "name": "BalloonRed", "outfit_name": "CharSkyKid_Prop_BalloonRed", "play_default_animation": true, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.25], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,1.0,1.0,1.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "White"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "CharRampEvents"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(0.0,0.0,0.0,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 2, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "volume": 30, "volume_type": "XL(Table)", "yOffset": 0.0}, {"name": "PastryTeaTable", "tea_set_drink_params": {"cup_count": "2", "cup_model": "Prop_PastryTeaTable_Cup", "cup_offset_rotations": ["0", "3.1415"], "cup_offsets": [[0.0, 5.3, -3.5], [0.0, 5.3, 3.5]], "cup_scale": "0.1", "cup_tea_model": "Prop_PastryTeaTable_Tea", "hold_cup_offset": ["0.070", "-0.025", "0.019"], "hold_cup_rot_x": "-1.570796", "hold_cup_rot_y": "-2.870796", "holding_cup_anim_count": "3", "holding_cup_anims": ["teaSaucerHold", "teaSaucerSip", "teaHold"], "saucer_model": "Prop_PastryTeaTable_Saucer", "sip_anim": "TeaSaucerAnim", "sip_empty_anim": "SipTeaSaucerEmpty", "sip_tea_icon": "UiOutfitPropTeacup", "sip_time": "2.5", "sips_per_cup": "3", "tea_height_full": "0.7"}}, {"code_type_name": "WisteriaTeaTable", "name": "WisteriaTeaTable"}, {"add_shadow_caster": false, "allow_roll": false, "allow_scale": false, "attribute_override": [], "attribute_override_amount": 0, "attribTex": "", "audio_material": 0, "candle_params": [], "category": "Special", "code_type_name": "DragonBoat", "diffuse1_scale": 1.0, "diffuse1Tex": "StoneBase", "diffuse2Tex": "StoneGrunge2", "base_rgba": [1.0, 1.0, 1.0, 0.0], "emission_scale": 0, "face_away": true, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 4.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, -0.5, 3)", "rotation": "(0,0,0)", "scale": "(1,1,1)"}], "max_place_distance": 15.0, "mesh": "BoatDayAnim_01", "name": "DragonBoat", "normal_scale": 1.0, "normTex": "UpNormal", "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "empty", "icon": "UiOutfitPropFortuneDrum", "chat_enabled": false}, "outfit_name": "CharSkyKid_Prop_DragonBoat", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": false, "requires_water": true, "scale": [0.459, 0.361, 0.292], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "selfLit": [0.1, 3.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"name": "AP10Spotlight_01", "light_params": [{"color": "(1.0,0.24,0.0625,1)", "intensity": 1, "offset": "(0.0,-15.06,0.0)", "radius": 4.5, "sharpness": 0.1}, {"color": "(1.0,0.3,0.125,1)", "intensity": 3.6, "offset": "(0.0,-90.0,0.0)", "radius": 8, "sharpness": 0.5}]}, {"name": "AP14VaseBouquet_01", "scale": [1.0]}, {"name": "MuralAir", "base_rgba": [1.0, 1.0, 1.0, 1.0]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "base_rgba": [1.0, 1.0, 1.0, 1.0]}, {"name": "MuralFire", "base_rgba": [1.0, 1.0, 1.0, 1.0]}, {"name": "MuralWater", "base_rgba": [1.0, 1.0, 1.0, 1.0]}, {"name": "StoneDiningTableSquare", "on_tap_play_anim_params": {"anim": "empty", "chat_enabled": true}}, {"name": "StoneKitchenStove", "marker_params": [{"offset": "(0, 0.05, -1)", "rotation": "(180,0,0)"}]}, {"name": "StoneKitchenOven", "marker_params": [{"offset": "(0, 0.03, -1)", "rotation": "(180,0,0)"}]}, {"allow_roll": true, "allow_scale": false, "attribute_override": [], "attribute_override_amount": 0, "attribTex": "Attrib_General", "audio_material": 0, "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "diffuse1_scale": 1.0, "diffuse1Tex": "CharRampS16Aurora", "diffuse2Tex": "White", "base_rgba": [1.0, 1.0, 1.0, 1.0], "drop_shadow_radius": 0.0, "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 4.5, -6.5)", "rotation": "(180,0,0)", "scale": "(1.0, 1.0, 1.0)"}, {"offset": "(6.5, 4.5, 0)", "rotation": "(90,0,0)", "scale": "(1.0, 1.0, 1.0)"}, {"offset": "(0, 4.5, 6.5)", "rotation": "(0,0,0)", "scale": "(1.0, 1.0, 1.0)"}], "max_place_distance": 15.0, "mesh": "CharSkyKid_Prop_SunflowerScreen", "name": "SunflowerScreen", "normal_scale": 0.2, "normTex": "UpNormal", "outfit_name": "CharSkyKid_Prop_SunflowerScreen", "pattern_hsv": [0.0, 0.0, 100.0], "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_hsv": [0.0, 0.0, 100.0], "selfLit": [0.0, 0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shared_space_default_count": 2, "shader_params": null, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"allow_roll": true, "allow_scale": false, "attribute_override": [], "attribute_override_amount": 0, "attribTex": "Attrib_General", "audio_material": 0, "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "diffuse1_scale": 1.0, "diffuse1Tex": "CharRampS16Aurora", "diffuse2Tex": "White", "base_rgba": [1.0, 1.0, 1.0, 1.0], "drop_shadow_radius": 0.0, "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [{"color": "(0.8,0.25,0.15,1)", "intensity": 1.5, "offset": "(0.0,15.0,0.0)", "radius": 7.5, "sharpness": 0.15}], "marker_params": [{"offset": "(0, 4.5, -6.5)", "rotation": "(180,0,0)", "scale": "(1.0, 1.0, 1.0)"}, {"offset": "(6.5, 4.5, 0)", "rotation": "(90,0,0)", "scale": "(1.0, 1.0, 1.0)"}, {"offset": "(0, 4.5, 6.5)", "rotation": "(0,0,0)", "scale": "(1.0, 1.0, 1.0)"}], "max_place_distance": 15.0, "mesh": "CharSkyKid_Prop_SunflowerLamp", "name": "SunflowerLamp", "normal_scale": 0.2, "normTex": "UpNormal", "outfit_name": "CharSkyKid_Prop_SunflowerLamp", "pattern_hsv": [0.0, 0.0, 100.0], "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_hsv": [0.0, 0.0, 100.0], "selfLit": [0.0, 0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shared_space_default_count": 2, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"name": "AP23WallPosterPiano<PERSON>s", "allow_roll": true}, {"name": "AP11Rose_01", "shader": "MeshSlNoShadows"}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 32, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [{"offset": "(1, 0, -16.0)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(-11, 0, -12.7)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(-16, 0, 0.1)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(-11, 0, 12.3)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(-0.3, 0, 17.1)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(12,0.0,12.0)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(17.0,0.0,0.3)", "scale": "(9.0,9.0,9.0)"}, {"offset": "(13.0,0.0,-10.43)", "scale": "(9.0,9.0,9.0)"}], "category": "Tables", "code_type_name": "WerewolfGameTable", "diffuse1Tex": "CharRampLNY", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": {"offset": "(0.0,3.5,0.0)", "scale": 6}, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(1, -4.6, -22.7)", "rotation": "(180,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(-16.8, -4.6, -17.7)", "rotation": "(-135,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(-24, -4.6, 0)", "rotation": "(-90,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(-16.0,-4.6, 18)", "rotation": "(-45,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(0, -4.6, 25.0)", "rotation": "(0,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(17.0, -4.6, 18)", "rotation": "(45,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(24.9, -4.6, 0)", "rotation": "(90,0,0)", "scale": "(1.5,1.0,1.5)"}, {"offset": "(18.4, -4.6, -14)", "rotation": "(135,0,0)", "scale": "(1.5,1.0,1.5)"}], "max_place_distance": 15.0, "mesh": "Prop_WerewolveTable", "name": "WerewolveTable", "normTex": "", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "Sit", "chat_enabled": true}, "outfit_name": "CharSkyKid_Prop_WerewolveTable", "place_audio_type": "Wood<PERSON><PERSON>vy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 1.0, "shared_space_default_count": 0, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Tables", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_StoneTableSmall", "name": "StoneTableSmallIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_StoneTableSmallIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 15, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Chairs", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 0.35, -0.25"}], "max_place_distance": 15.0, "mesh": "P_StoneSofaSide", "name": "StoneSofaSideIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSitStoneSofa"}, "outfit_name": "CharSkyKid_Prop_StoneSofaSideIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 4, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 4, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 10, "volume_type": "S(Pot)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Tables", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_StoneConsoleTable", "name": "StoneConsoleTableIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_StoneConsoleTableIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 20, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Chairs", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 0.34, -0.15)", "scale": 0.25}], "max_place_distance": 15.0, "mesh": "P_StoneStool", "name": "StoneStoolIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSitStoneStool"}, "outfit_name": "CharSkyKid_Prop_StoneStoolIceNe", "place_audio_type": "StoneMedium", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 4, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 4, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 5, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Counters", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_StoneKitchenDrawers", "name": "StoneKitchenDrawersIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_StoneKitchenDrawersIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 2, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 15, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Cooking", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": {"offset": "(0.0,0.25,-0.1)", "scale": 0.25, "sound_type": "17"}, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 0, -1)", "rotation": "(180,0,0)"}], "max_place_distance": 15.0, "mesh": "P_StoneKitchen<PERSON>tove", "name": "StoneKitchenStoveIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "SitWarmHands"}, "outfit_name": "CharSkyKid_Prop_StoneKitchenStoveIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 15, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Cooking", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": {"offset": "(0.0,0.75,0.1)", "scale": 0.3, "sound_type": "17"}, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 0, -1)", "rotation": "(180,0,0)"}], "max_place_distance": 15.0, "mesh": "P_StoneKitchenOven", "name": "StoneKitchenOvenIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "StandWarmHands", "icon": "UiEmoteSit0"}, "outfit_name": "CharSkyKid_Prop_StoneKitchenOvenIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 15, "volume_type": "<PERSON>(Chair)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Beds", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0, 0.5, 0.0)", "rotation": "(0.0, -10.0, 0.0)"}], "max_place_distance": 15.0, "mesh": "P_StoneBedSingle", "name": "StoneBedSingleIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSleepBed", "icon": "UiEmoteAP06Doze", "on_enter_sound_name": "ToSleepOnBed"}, "outfit_name": "CharSkyKid_Prop_StoneBedSingleIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [15.0, 25.0, 80.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Storage", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_StoneDresserTall", "name": "StoneDresserTallIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_StoneDresserTallIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [15.0, 25.0, 80.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 2, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 10, "volume_type": "S(Pot)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [{"offset": "(-0.425,0.6,0.09)", "scale": "(1.25,0.35,1.25)"}, {"offset": "(-0.315,0.63,0.275)", "scale": "(1.0,0.5,1.0)"}], "category": "Bathing", "code_type_name": "BathTub", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [{"offset": "(0.5, 0.3, 0)", "rotation": "(90,0,0)"}], "max_place_distance": 15.0, "mesh": "P_StoneBathtubLarge", "name": "StoneBathtubLargeIceNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "SitBathTubLarge", "on_exit_sound_name": "BathOut", "on_tap_sound_name": "BathIn"}, "outfit_name": "CharSkyKid_Prop_StoneBathtubLargeIceNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 2, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_IceBlockHorizontalNe", "name": "IceBlockHorizontalNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {}, "outfit_name": "CharSkyKid_Prop_IceBlockHorizontalNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 10, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 10, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 1, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_IceBlockVeritcalNe", "name": "IceBlockVeritcalNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {}, "outfit_name": "CharSkyKid_Prop_IceBlockVeritcalNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 10, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 10, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 1, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_IceSlideConcaveNe", "name": "IceSlideConcaveNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {}, "outfit_name": "CharSkyKid_Prop_IceSlideConcaveNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 10, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 10, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 1, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": false, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 22, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "depth_prepass": true, "diffuse1Tex": "StoneBase", "diffuse1_scale": 1.0, "diffuse2Tex": "PropRampStoneTint", "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_IceSlideConvexNe", "name": "IceSlideConvexNe", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {}, "outfit_name": "CharSkyKid_Prop_IceSlideConvexNe", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_hsv": [210.0, 30.0, 90.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_hsv": [210.0, 35.0, 70.0], "selfLit": [0.0, 0.0, 0.0], "shader": "Ice", "shader_params": {"colors": [{"shader_param_name": "u_baseColor", "value": "(0.34,0.6,0.79,1.0)"}], "floats": [{"shader_param_name": "u_alpha", "value": 0.5}, {"shader_param_name": "u_distertionIntensity", "value": 0.1}, {"shader_param_name": "u_emission", "value": 1.0}, {"shader_param_name": "u_metalness", "value": 1}, {"shader_param_name": "specularScatter", "value": 0.05}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "CliffDamaged"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "IceNorm"}], "vector4s": [{"shader_param_name": "u_sss", "value": "(0.0,10.0,10.0,0.1)"}, {"shader_param_name": "u_uv", "value": "(1.0,1.0,0.8,0.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 5, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 5, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "<PERSON><PERSON><PERSON>", "variantTex": "", "volume": 1, "volume_type": "XS(Cup)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribute_override": [], "attribute_override_amount": 0, "attribTex": "", "audio_material": 52, "candle_params": [], "category": "Special", "code_type_name": "CloudMusicHomeListening", "diffuse1_scale": 1.0, "diffuse1Tex": "StoneBase", "diffuse2Tex": "CharRampEvents", "base_rgba": [1.0, 1.0, 1.0, 1.0], "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_CloudMusicHomeListening", "name": "CloudMusicHomeListening", "normal_scale": 1.0, "normTex": "StoneBaseSh", "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSit2", "chat_enabled": true}, "outfit_name": "CharSkyKid_Prop_CloudMusicHomeListening", "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.075], "secondary_dye_hsv": [0.0, 0.0, 100.0], "secondary_base_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": {}, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "volume": 30, "volume_type": "L(Bed)", "yOffset": 0.0}, {"name": "MannequinFigurine", "yOffset": 0.5}, {"name": "CrabbitPortal", "yOffset": 0.1}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 52, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Chairs", "code_type_name": "Hammock", "diffuse1Tex": "CharRampS16Aurora", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "dyeable_primary": false, "dyeable_secondary": false, "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "instrument_params": null, "light_params": [], "marker_params": [{"offset": "(0, 0, 0)"}], "max_place_distance": 15.0, "mesh": "Prop_BoatRaceHammock", "name": "BoatRaceHammock", "normTex": "", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "Sleep", "icon": "UiEmoteAP06Doze"}, "outfit_name": "CharSkyKid_Prop_BoatRaceHammock", "place_audio_type": "Wood<PERSON><PERSON>", "play_default_animation": false, "primary_dye_cost": 0, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [1.0], "secondary_base_hsv": [0.0, 0.0, 100.0], "secondary_dye_cost": 0, "secondary_dye_hsv": [0.0, 0.0, 100.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 0.3, "shared_space_default_count": 3, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 3, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 20, "volume_type": "L(Bed)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "Attrib_General", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 0, "base_rgba": [0.8, 0.8, 0.8, 1.0], "candle_params": [], "category": "MiscellaneousLarge", "code_type_name": "Pizzahut", "diffuse1Tex": "<PERSON><PERSON><PERSON>amp<PERSON><PERSON><PERSON>", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "dyeable_primary": false, "dyeable_secondary": false, "emission_scale": 1.0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "instrument_params": null, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_BskPisaTray", "name": "Pizzahut", "normTex": "", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_Pizzahut", "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_dye_cost": 0, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.25], "secondary_base_hsv": [0.0, 0.0, 100.0], "secondary_dye_cost": 0, "secondary_dye_hsv": [0.0, 0.0, 100.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 10, "volume_type": "S(Pot)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "Attrib_General", "attribute_override": [1.0, 1.0, 0.1, 1.0], "attribute_override_amount": 1, "audio_material": 0, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "MiscellaneousLarge", "code_type_name": "Pizzahut", "diffuse1Tex": "White", "diffuse1_scale": 1.0, "diffuse2Tex": "CharRampLNY", "dyeable_primary": false, "dyeable_secondary": false, "emission_scale": 1.0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "instrument_params": null, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "Prop_PizzaPlate", "name": "LevelPizzahut", "normTex": "", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": null, "outfit_name": "CharSkyKid_Prop_LevelPizzahut", "place_audio_type": "DefaultLight", "play_default_animation": false, "primary_dye_cost": 0, "primary_dye_hsv": [0.0, 0.0, 100.0], "recordable": true, "requires_water": false, "scale": [0.25], "secondary_base_hsv": [0.0, 0.0, 100.0], "secondary_dye_cost": 0, "secondary_dye_hsv": [0.0, 0.0, 100.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshPlaceableProp", "shader_params": {"colors": [{"shader_param_name": "u_diffuseColor", "value": "(1.0,0.7,0.5,0.0)"}], "floats": [{"shader_param_name": "u_diffuse1TexScale", "value": 1.0}, {"shader_param_name": "u_normTexScale", "value": 1.0}], "textures": [{"shader_param_name": "u_diffuse1Tex", "texture_name": "White"}, {"shader_param_name": "u_diffuse2Tex", "texture_name": "CharRampLNY"}, {"shader_param_name": "u_lightTex", "texture_name": "White"}, {"shader_param_name": "u_normTex", "texture_name": "UpNormal"}], "vector4s": [{"shader_param_name": "u_selfLit", "value": "(1.0,0.2,0.0,1.0)"}]}, "shadow_scale": 1.0, "shared_space_default_count": 0, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 0, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 10, "volume_type": "S(Pot)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "Attrib_General", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 0, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Beds", "code_type_name": "Placeable", "diffuse1Tex": "CharRampLNY", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "dyeable_primary": false, "dyeable_secondary": false, "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "instrument_params": null, "light_params": [], "marker_params": [{"offset": "(0, 6.5, 0.0)", "rotation": "(0.0, -10.0, 0.0)"}], "max_place_distance": 15.0, "mesh": "P_SunflowerBed", "name": "SunflowerBed", "normTex": "UpNormal", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {"anim": "GoSleepBed", "icon": "UiEmoteAP06Doze", "on_enter_sound_name": "ToSleepOnBed"}, "outfit_name": "CharSkyKid_Prop_SunflowerBed", "place_audio_type": "StoneMedium", "play_default_animation": false, "primary_dye_cost": 0, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_cost": 0, "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 1.0, "shared_space_default_count": 1, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 1, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 25, "volume_type": "L(Bed)", "yOffset": 0.0}, {"add_shadow_caster": true, "allow_roll": true, "allow_scale": false, "attribTex": "Attrib_General", "attribute_override": [], "attribute_override_amount": 0, "audio_material": 0, "base_rgba": [1.0, 1.0, 1.0, 1.0], "candle_params": [], "category": "Furniture", "code_type_name": "Placeable", "diffuse1Tex": "CharRampS11", "diffuse1_scale": 1.0, "diffuse2Tex": "White", "dyeable_primary": false, "dyeable_secondary": false, "emission_scale": 0, "face_away": false, "face_down": false, "flame_params": null, "force_floor": false, "forward_offset": 2.0, "free_needed_likes_count": "", "hang_offset": 0.0, "has_collision": true, "instrument_params": null, "light_params": [], "marker_params": [], "max_place_distance": 15.0, "mesh": "P_SunflowerWreath", "name": "SunflowerWreath", "normTex": "StoneBaseSh", "normal_scale": 1.0, "on_off_toggle_params": null, "on_tap_play_anim_params": {}, "outfit_name": "CharSkyKid_Prop_SunflowerWreath", "place_audio_type": "StoneHeavy", "play_default_animation": false, "primary_dye_cost": 0, "primary_dye_hsv": [0.0, 0.0, 0.0], "recordable": true, "requires_water": false, "scale": [0.1], "secondary_base_hsv": [0.0, 0.0, 0.0], "secondary_dye_cost": 0, "secondary_dye_hsv": [0.0, 0.0, 0.0], "selfLit": [0.0, 0.0, 0.0], "shader": "MeshSlNoShadows", "shader_params": null, "shadow_scale": 1.0, "shared_space_default_count": 4, "shared_space_free_in_levels": [], "shared_space_free_in_realms": [], "shared_space_placement_count": 4, "tea_set_drink_params": null, "tea_set_food_params": null, "upgrade_base": "", "upgrade_type": "", "variantTex": "", "volume": 10, "volume_type": "S(Pot)", "yOffset": 0.0}]