[{"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdchase_1", "landmark": "Sunset", "npc": "amazed", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_chase_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdchase_2", "landmark": "Night_Shelter", "npc": "ap26_mourner", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_chase_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdchase_3", "landmark": "Sunset_Theater", "npc": "ap26_cousin", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_chase_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdchase_4", "landmark": "Rain", "npc": "ap26_lumberjack", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_chase_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdfeather_1", "landmark": "Sunset", "npc": "amazed", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_feather_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdfeather_2", "landmark": "Night_Shelter", "npc": "ap26_mourner", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_feather_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdfeather_3", "landmark": "Sunset_Theater", "npc": "ap26_cousin", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_feather_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bluebirdfeather_4", "landmark": "Rain", "npc": "ap26_lumberjack", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "blue_bird_feather_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "bow_at_a_player", "landmark": "", "npc": "bow", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "bowed_at_player_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_cape", "landmark": "", "npc": "acknowledge", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "cape_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_feet", "landmark": "", "npc": "injured", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "feet_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_hair", "landmark": "", "npc": "juggle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "hair_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_hat", "landmark": "", "npc": "crabvoice", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "hat_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_horn", "landmark": "", "npc": "bearhug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "horn_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_mask", "landmark": "", "npc": "deepbreath", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "mask_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_neck", "landmark": "", "npc": "marching", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "neck_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_pants", "landmark": "", "npc": "welcome", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "pants_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "change_prop", "landmark": "", "npc": "nerdy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "prop_change_count"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "cherry_sappling_dusk_01", "landmark": "Dusk", "npc": "crabvoice", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "cherry_sappling_dusk_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "cherry_sappling_night_01", "landmark": "Night", "npc": "shh", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "cherry_sappling_night_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "cherry_sappling_prairie_01", "landmark": "Prairie_ButterflyFields_Town", "npc": "doublefive", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "cherry_sappling_prairie_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "cherry_sappling_rain_01", "landmark": "Rain", "npc": "lazycool", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "cherry_sappling_rain_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "cherry_sappling_sunset_01", "landmark": "Sunset_Town", "npc": "tripleaxel", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "cherry_sappling_sunset_01_time"}, {"difficulty": "very_easy", "display_type": "count", "icon": "", "id": "craft_a_thing", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "friend_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "do_competition_play", "landmark": "SunsetColosseum", "npc": "proud", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location1_1", "landmark": "MainStreet", "npc": "joy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "bowed_at_player_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location1_2", "landmark": "MainStreet", "npc": "joy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_high_five_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "domusic_location2_1", "landmark": "MainStreet", "npc": "kungfu", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "new_acquaintance_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location2_2", "landmark": "MainStreet", "npc": "kungfu", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "collectible_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "domusic_location3_1", "landmark": "Sunset_Theater", "npc": "respect", "requires_questgiver": false, "stat_delta": 20.0, "stat_group_type": "explore", "stat_type": "light_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location3_2", "landmark": "Sunset_Theater", "npc": "respect", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "forged_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location4_1", "landmark": "MainStreet_ConcertHall", "npc": "think", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "sent_gift_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location4_2", "landmark": "MainStreet_ConcertHall", "npc": "think", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "wave_at_friend_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "domusic_location5_1", "landmark": "NightDesert", "npc": "headbob", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "new_acquaintance_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location5_2", "landmark": "NightDesert", "npc": "headbob", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "night_collectibles_collected_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "domusic_location5_3", "landmark": "NightDesert", "npc": "headbob", "requires_questgiver": false, "stat_delta": 20.0, "stat_group_type": "explore", "stat_type": "light_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location6_1", "landmark": "SunsetColosseum", "npc": "duetdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "express_an_emote_to_friends_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location6_2", "landmark": "SunsetColosseum", "npc": "duetdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "forged_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "domusic_location6_3", "landmark": "SunsetColosseum", "npc": "duetdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "bowed_at_player_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_dusk_triangle", "landmark": "Dusk_Triangle", "npc": "anxious", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "pants_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_dusk_triangle_2", "landmark": "Dusk_Triangle", "npc": "anxious", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "runway_recording_shrine_view_count"}, {"difficulty": "very_easy", "display_type": "count", "icon": "", "id": "dostyle_duskmid", "landmark": "DuskMid", "npc": "shrug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "mask_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_night2", "landmark": "Night2", "npc": "shh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "runway_recording_shrine_walk_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_night2_2", "landmark": "Night2", "npc": "wise", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_hand_hold_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_night_paintedworld", "landmark": "Night_PaintedWorld", "npc": "muscle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "bowed_at_player_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "dostyle_prairie_island", "landmark": "Prairie_Island", "npc": "jelly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "jellyfish_charge_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_prairie_island_2", "landmark": "Prairie_Island", "npc": "bellyscratch", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_high_five_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_prairie_nestandkeeper", "landmark": "Prairie_NestAndKeeper", "npc": "dance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "express_an_emote_to_friends_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_prairie_wildlifepark", "landmark": "Prairie_WildLifePark", "npc": "sidehug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "hair_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_rain", "landmark": "Rain", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "neck_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_rain_2", "landmark": "Rain", "npc": "chuckle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "prop_change_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "dostyle_rainshelter", "landmark": "RainShelter", "npc": "tsktsk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rain_grandmatable_count"}, {"difficulty": "very_easy", "display_type": "count", "icon": "", "id": "dostyle_yetipark", "landmark": "Sunset_YetiPark", "npc": "spintrick", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "cape_change_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_day_1", "landmark": "Prairie_Island", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_day_2", "landmark": "Prairie_Island", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_day_3", "landmark": "Prairie_Island", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_dusk_1", "landmark": "Dusk_Triangle", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_dusk_2", "landmark": "Dusk_Triangle", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_night_1", "landmark": "NightDesert", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_night_2", "landmark": "NightDesert", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_night_3", "landmark": "NightDesert", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_rain_1", "landmark": "RainForest", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_rain_2", "landmark": "RainForest", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_rain_3", "landmark": "RainForest", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_sunset_1", "landmark": "SunsetVillage", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_sunset_2", "landmark": "SunsetVillage", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuXTreasure", "id": "dotreasure_sunset_3", "landmark": "SunsetVillage", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "do_treasure_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "express_an_emote_to_a_friend", "landmark": "", "npc": "laugh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "express_an_emote_to_friends_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_earth", "landmark": "Prairie_NestAndKeeper", "npc": "scare", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_earth_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_fire", "landmark": "RainMid", "npc": "playfight", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_fire_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_mind", "landmark": "NightArchive", "npc": "shrug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_mind_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_void", "landmark": "DuskOasis", "npc": "crabwalk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_void_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_water", "landmark": "Sunset", "npc": "doze", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_water_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fetchlight_wind", "landmark": "Dawn", "npc": "nod", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "fetchlight_wind_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_flowers_1", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_flowers_2", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_rolling", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_sleeping_1", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_sleeping_2", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "find_cafe_sleeping_3", "landmark": "MainStreet", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "cafe_quest_found"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "fly_with_a_manta", "landmark": "", "npc": "babymanta", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "bird_attach_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "follow_another_player", "landmark": "", "npc": "seek", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "follow_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "forge_a_candle", "landmark": "", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "forged_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "high_five_someone", "landmark": "", "npc": "handstand", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_high_five_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "hold_someones_hand", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_hand_hold_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "hug_someone", "landmark": "", "npc": "cold", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "non_unique_hug_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "light_20_candles", "landmark": "", "npc": "wise", "requires_questgiver": false, "stat_delta": 20.0, "stat_group_type": "explore", "stat_type": "light_candle_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "lightseeker_forest", "landmark": "Rain", "npc": "lazycool", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "lightseeker_forest_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "lightseeker_prairie", "landmark": "<PERSON><PERSON><PERSON>F<PERSON>s", "npc": "doublefive", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "lightseeker_prairie_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "lightseeker_valley", "landmark": "Sunset", "npc": "tripleaxel", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "lightseeker_valley_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "lightseeker_vault", "landmark": "Night", "npc": "shh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "lightseeker_vault_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "lightseeker_wasteland", "landmark": "Dusk", "npc": "crabwalk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "lightseeker_wasteland_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "make_a_new_acquaintance", "landmark": "", "npc": "come", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "new_acquaintance_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_dusk_crabfields", "landmark": "Dusk_CrabField", "npc": "salute", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_dusk_crabfields_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_dusk_duskmid", "landmark": "DuskMid", "npc": "lookaround", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_dusk_duskmid_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_dusk_graveyard", "landmark": "DuskGraveyard", "npc": "sneaky", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_dusk_graveyard_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_dusk_main", "landmark": "Dusk", "npc": "die", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_dusk_main_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_dusk_oasis", "landmark": "DuskOasis", "npc": "doze", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_dusk_oasis_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_night_main", "landmark": "Night_ThirdFloor", "npc": "force", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_night_main_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_night_main2", "landmark": "Night", "npc": "love", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_night_main2_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_night_night2", "landmark": "Night2_SecondFloor", "npc": "pray", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_night_night2_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_butterfly", "landmark": "<PERSON><PERSON><PERSON>F<PERSON>s", "npc": "butterfly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_butterfly_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_cave", "landmark": "Prairie_Cave", "npc": "wipe", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_cave_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_island", "landmark": "Prairie_Island", "npc": "bellyscratch", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_island_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_nestkeeper", "landmark": "Prairie_NestAndKeeper", "npc": "deepbreath", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_nestkeeper_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_village_faerie", "landmark": "Prairie_Village", "npc": "celebrate", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_village_faerie_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_prairie_village_koi", "landmark": "Prairie_Village_Shrine", "npc": "bird", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_prairie_village_koi_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_rain_main", "landmark": "Rain", "npc": "pout", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_rain_main_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_rain_rainend", "landmark": "RainEnd_Pond", "npc": "sorry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_rain_rainend_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_rain_rainforest", "landmark": "RainForest", "npc": "shy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_rain_rainforest_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_rain_rainmid", "landmark": "RainMid", "npc": "ohno", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_rain_rainmid_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_rain_shelter", "landmark": "RainShelter", "npc": "cold", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_rain_shelter_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_sunset_citadel", "landmark": "Sunset_Citadel", "npc": "bow", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_sunset_citadel_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_sunset_citadel2", "landmark": "Sunset_Citadel", "npc": "backflip", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_sunset_citadel2_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_sunset_main", "landmark": "Sunset", "npc": "strong", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_sunset_main_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "meditate_sunset_raceend", "landmark": "SunsetColosseum", "npc": "manta", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "meditate_sunset_raceend_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "melt_10_darkstones", "landmark": "", "npc": "wipe", "requires_questgiver": false, "stat_delta": 10.0, "stat_group_type": "collect", "stat_type": "darkstone_melted_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "pickup_30_wax", "landmark": "", "npc": "lookaround", "requires_questgiver": false, "stat_delta": 30.0, "stat_group_type": "explore", "stat_type": "wax_pickup_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_cloudcollect", "landmark": "SunsetColosseum", "npc": "shy", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_crabrace", "landmark": "SunsetColosseum", "npc": "brave", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_rainrace", "landmark": "SunsetColosseum", "npc": "shy", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_speedskate", "landmark": "SunsetColosseum", "npc": "backflip", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_voidcollect", "landmark": "SunsetColosseum", "npc": "backflip", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "play_tot_sport_wingsuiting", "landmark": "SunsetColosseum", "npc": "laugh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "do_competition_play_sports"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "rainbow_rainbow_dusk_01", "landmark": "Dusk_Triangle", "npc": "anxious", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "rainbow_rainbow_dusk_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "rainbow_rainbow_night_01", "landmark": "NightDesert", "npc": "wise", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "rainbow_rainbow_night_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "rainbow_rainbow_prairie_01", "landmark": "Prairie_Island", "npc": "rally", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "rainbow_rainbow_prairie_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "rainbow_rainbow_rain_01", "landmark": "Skyway", "npc": "nerdy", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "rainbow_rainbow_rain_01_time"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "rainbow_rainbow_sunset_01", "landmark": "Sunset_YetiPark", "npc": "bearhug", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "rainbow_rainbow_sunset_01_time"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "receive_a_gift", "landmark": "", "npc": "gratitude", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "received_gift_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "recharge_by_avatar", "landmark": "", "npc": "carry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "avatar_charge_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "recharge_by_flower", "landmark": "", "npc": "deepbreath", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "flower_charge_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "recharge_by_jellyfish", "landmark": "", "npc": "jelly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "jellyfish_charge_count"}, {"difficulty": "hard", "display_type": "count", "icon": "", "id": "recharge_by_shroom", "landmark": "", "npc": "doublefive", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "shroom_charge_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "rescue_a_manta_from_darkstone", "landmark": "RainMid", "npc": "cry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "birds_released_count"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteAP14Awww", "id": "runway_recording_shrine_view", "landmark": "", "npc": "awww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "runway_recording_shrine_view_count"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteAP10Marching", "id": "runway_recording_shrine_walk", "landmark": "", "npc": "marching", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "runway_recording_shrine_walk_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit", "landmark": "", "npc": "shh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "collectible_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit_in_day", "landmark": "", "npc": "wave", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "day_collectibles_collected_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit_in_dusk", "landmark": "", "npc": "sneaky", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "dusk_collectibles_collected_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit_in_night", "landmark": "", "npc": "pray", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "night_collectibles_collected_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit_in_rain", "landmark": "", "npc": "cry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "rain_collectibles_collected_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "save_a_spirit_in_sunset", "landmark": "", "npc": "backflip", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "narrative", "stat_type": "sunset_collectibles_collected_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "seen_by_dark_creature", "landmark": "DuskStart", "npc": "sneaky", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "seen_by_dark_creature_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "send_a_gift", "landmark": "", "npc": "kiss", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "sent_gift_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "shoute_at_5_crabs", "landmark": "", "npc": "crabwalk", "requires_questgiver": false, "stat_delta": 5.0, "stat_group_type": "collect", "stat_type": "shoute_at_crab_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "sit_at_a_bench_with_a_stranger", "landmark": "", "npc": "grief", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "sit_at_a_bench_with_a_stranger_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_angler1", "landmark": "Dusk_Triangle", "npc": "anxious", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_angler"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_angler2", "landmark": "Dusk", "npc": "anxious", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_angler"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_angler3", "landmark": "Dusk_CrabField", "npc": "anxious", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_angler"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "spirit_anniversary", "landmark": "MainStreet", "npc": "tiptoe", "requires_questgiver": true, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "spirit_anniversary"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_beachday1", "landmark": "Dusk_Triangle", "npc": "<PERSON><PERSON>gh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_beachday"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_beachday2", "landmark": "Dusk_Triangle", "npc": "ouch", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_beachday"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_beachday3", "landmark": "Dusk_Triangle", "npc": "anxious", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_beachday"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_boatrepair1", "landmark": "Dusk_Triangle", "npc": "ouch", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_boatrepair"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_boatrepair2", "landmark": "Dusk_Triangle", "npc": "ouch", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_boatrepair"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_boatrepair3", "landmark": "Dusk_Triangle", "npc": "wait", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_boatrepair"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_bookgift1", "landmark": "SunsetVillage", "npc": "duetdance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_bookgift"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_bookgift2", "landmark": "SunsetVillage", "npc": "awww", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_bookgift"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_bookgift3", "landmark": "SunsetVillage", "npc": "awww", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_bookgift"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteButterfly", "id": "spirit_butterfly", "landmark": "<PERSON><PERSON><PERSON>F<PERSON>s", "npc": "butterfly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_butterfly_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_camping1", "landmark": "Prairie_WildLifePark", "npc": "sidehug", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_camping"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_camping2", "landmark": "Prairie_WildLifePark", "npc": "sidehug", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_camping"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_camping3", "landmark": "Prairie_WildLifePark", "npc": "sidehug", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_camping"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_catapult1", "landmark": "DuskGraveyard", "npc": "<PERSON><PERSON>gh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_catapult"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_catapult2", "landmark": "DuskGraveyard", "npc": "<PERSON><PERSON>gh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_catapult"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_catapult3", "landmark": "DuskGraveyard", "npc": "<PERSON><PERSON>gh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_catapult"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_chimesmithbuilder1", "landmark": "Prairie_NestAndKeeper", "npc": "voila", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_chimesmithbuilder"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_chimesmithbuilder2", "landmark": "Prairie_NestAndKeeper", "npc": "nerdy", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_chimesmithbuilder"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_chimesmithbuilder3", "landmark": "Prairie_NestAndKeeper", "npc": "nerdy", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_chimesmithbuilder"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteCold", "id": "spirit_cold", "landmark": "RainForest", "npc": "cold", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_cold_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_discoverark1", "landmark": "DuskOasis", "npc": "ouch", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_discoverark"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_discoverark2", "landmark": "DuskOasis", "npc": "<PERSON><PERSON>gh", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_discoverark"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_discoverark3", "landmark": "DuskOasis", "npc": "wait", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_discoverark"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_duets1", "landmark": "NightDesert_Beach", "npc": "questap23", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_duets"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_exploresanctuary1", "landmark": "Prairie_Island", "npc": "nightbird", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_exploresanctuary"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_exploresanctuary2", "landmark": "Prairie_Island", "npc": "jollydance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_exploresanctuary"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_exploresanctuary3", "landmark": "Prairie_Island", "npc": "windpose", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_exploresanctuary"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "spirit_flightguide1", "landmark": "Prairie_Village", "npc": "questap12", "requires_questgiver": true, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "spirit_flightguide"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "spirit_flightguide2", "landmark": "Prairie_Village", "npc": "questap12", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_flightguide"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "spirit_flightguide3", "landmark": "DayEnd", "npc": "questap12", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_flightguide"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_forgottenstory1", "landmark": "SunsetVillage", "npc": "awww", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_forgottenstory"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_forgottenstory2", "landmark": "SunsetVillage", "npc": "awww", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_forgottenstory"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_forgottenstory3", "landmark": "SunsetVillage", "npc": "awww", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_forgottenstory"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_grieving1", "landmark": "RainMid", "npc": "sorry", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_grieving"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_grieving2", "landmark": "RainMid", "npc": "sorry", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_grieving"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_grieving3", "landmark": "RainMid", "npc": "ohno", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_grieving"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_grieving4", "landmark": "RainMid", "npc": "whale", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_grieving"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_grieving5", "landmark": "RainMid", "npc": "ohno", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_grieving"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_helpinghand1", "landmark": "RainForest", "npc": "cry", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_helpinghand"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_helpinghand2", "landmark": "RainMid", "npc": "pout", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_helpinghand"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_helpinghand3", "landmark": "RainMid", "npc": "sorry", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_helpinghand"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_hidenseek1", "landmark": "RainMid", "npc": "seek", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_hide<PERSON><PERSON>"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_hidenseek2", "landmark": "RainShelter", "npc": "seek", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_hide<PERSON><PERSON>"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_hidenseek3", "landmark": "Rain", "npc": "seek", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_hide<PERSON><PERSON>"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_musicianrace1", "landmark": "SunsetVillage", "npc": "headbob", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_musicianrace"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_musicianrace2", "landmark": "SunsetVillage", "npc": "headbob", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_musicianrace"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_musicianrace3", "landmark": "SunsetVillage", "npc": "headbob", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_musicianrace"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_navigator1", "landmark": "Prairie_NestAndKeeper", "npc": "babymanta", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_navigator"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_navigator2", "landmark": "Prairie_NestAndKeeper", "npc": "pointup", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_navigator"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_navigator3", "landmark": "Prairie_NestAndKeeper", "npc": "pointup", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_navigator"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_newchoreo1", "landmark": "SunsetVillage", "npc": "duetdance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_newchoreo"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_newchoreo2", "landmark": "SunsetVillage", "npc": "duetdance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_newchoreo"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_newchoreo3", "landmark": "SunsetVillage", "npc": "duetdance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_newchoreo"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_plead1", "landmark": "Night_Shelter", "npc": "plead", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_plead"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_plead2", "landmark": "Night_Shelter", "npc": "plead", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_plead"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_plead3", "landmark": "Night_Shelter", "npc": "plead", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_plead"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_production1", "landmark": "Sunset_Theater", "npc": "handshake", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_production"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_production2", "landmark": "Sunset_Theater", "npc": "handshake", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_production"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_production3", "landmark": "Sunset_Theater", "npc": "handshake", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_production"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_rockcollection1", "landmark": "Prairie_WildLifePark", "npc": "jollydance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_rockcollection"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_rockcollection2", "landmark": "Prairie_WildLifePark", "npc": "jollydance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_rockcollection"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_rockcollection3", "landmark": "Prairie_WildLifePark", "npc": "jollydance", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_rockcollection"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_scouting1", "landmark": "Dusk_Triangle", "npc": "wait", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_scouting"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_scouting2", "landmark": "Dusk_Triangle", "npc": "wait", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_scouting"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_scouting3", "landmark": "Dusk_Triangle", "npc": "wait", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_scouting"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuCamera", "id": "spirit_selfie_crabvoice", "landmark": "Prairie_WildLifePark", "npc": "crabvoice", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_selfie_crabvoice_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuCamera", "id": "spirit_selfie_crabwalk", "landmark": "Prairie_WildLifePark", "npc": "crabwalk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_selfie_crabwalk_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuCamera", "id": "spirit_selfie_evillaugh", "landmark": "Prairie_WildLifePark", "npc": "<PERSON><PERSON>gh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_selfie_evillaugh_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuCamera", "id": "spirit_selfie_grumpy", "landmark": "Prairie_WildLifePark", "npc": "grumpy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_selfie_grumpy_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiMenuCamera", "id": "spirit_selfie_welcome", "landmark": "Prairie_WildLifePark", "npc": "welcome", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_selfie_welcome_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_stagehandmusician1", "landmark": "Sunset_Theater", "npc": "handshake", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_stagehandmusician"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_stagehandmusician2", "landmark": "Sunset_Theater", "npc": "handshake", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_stagehandmusician"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_stagehandmusician3", "landmark": "Sunset_Theater", "npc": "headbob", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_stagehandmusician"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_tiptoe1", "landmark": "Night_Shelter", "npc": "tiptoe", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_tiptoe"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_umbrella1", "landmark": "RainForest", "npc": "shy", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_umbrella"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_umbrella2", "landmark": "RainForest", "npc": "cold", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_umbrella"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_umbrella3", "landmark": "RainForest", "npc": "cold", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_umbrella"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_wildlife1", "landmark": "Prairie_Village", "npc": "nightbird", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_wildlife"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_wildlife2", "landmark": "Prairie_Village", "npc": "nightbird", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_wildlife"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_wildlife3", "landmark": "Prairie_Village", "npc": "nightbird", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_wildlife"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_yoga1", "landmark": "Prairie_Island", "npc": "windpose", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_yoga"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_yoga2", "landmark": "Prairie_Village", "npc": "windpose", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_yoga"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "spirit_yoga3", "landmark": "Prairie_WildLifePark", "npc": "windpose", "requires_questgiver": true, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "spirit_yoga"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_dusk_spitroast", "landmark": "DuskGraveyard", "npc": "dontgo", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_dusk_spitroast_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_island_pollution", "landmark": "Prairie_Island", "npc": "gratitude", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_island_pollution_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_night_waterfall", "landmark": "Night2", "npc": "float", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_night_waterfall_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_prairie_cozycave", "landmark": "Prairie_Cave", "npc": "salutation", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_prairie_cozycave_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rain_bonfirecamp", "landmark": "RainForest", "npc": "lazycool", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rain_bonfirecamp_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rain_grandmatable", "landmark": "RainShelter", "npc": "sorry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rain_grandmatable_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_rainbow_dusk", "landmark": "DuskOasis", "npc": "die", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbow_dusk_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_rainbow_night", "landmark": "Night2", "npc": "float", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbow_night_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_rainbow_prairie", "landmark": "Prairie_Cave", "npc": "rally", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbow_prairie_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_rainbow_rain", "landmark": "Rain", "npc": "bubbles", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbow_rain_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_rainbow_sunset", "landmark": "Sunset_Citadel", "npc": "proud", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbow_sunset_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rainbowdeux_dusk", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbowdeux_dusk_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rainbow<PERSON>ux_night", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbowdeux_night_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rainbowdeux_prairie", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbowdeux_prairie_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rainbowdeux_rain", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbowdeux_rain_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_rainbowdeux_sunset", "landmark": "", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_rainbowdeux_sunset_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_skyway_speedtube", "landmark": "Skyway", "npc": "voila", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_skyway_speedtube_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "visit_stormy_event", "landmark": "", "npc": "wait", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_stormy_event_count"}, {"difficulty": "fair", "display_type": "time", "icon": "", "id": "visit_sunset_hotspring", "landmark": "SunsetVillage", "npc": "peek", "requires_questgiver": false, "stat_delta": 60.0, "stat_group_type": "social", "stat_type": "visit_sunset_hotspring_time"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "visit_sunset_labyrinth", "landmark": "SunsetEnd2", "npc": "sparkler", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "explore", "stat_type": "visit_sunset_labyrinth_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "wave_at_a_friend", "landmark": "", "npc": "wave", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "social", "stat_type": "wave_at_friend_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap09_fetch_01", "landmark": "SunsetVillage", "npc": "spintrick", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap09_fetch_01_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap09_fetch_02", "landmark": "SunsetVillage", "npc": "peek", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap09_fetch_02_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap09_fetch_03", "landmark": "Sunset_YetiPark", "npc": "bearhug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap09_fetch_03_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap09_fetch_04", "landmark": "SunsetColosseum", "npc": "showdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap09_fetch_04_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_01", "landmark": "Rain", "npc": "questap10", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_01_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_02", "landmark": "RainForest", "npc": "marching", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_02_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_03", "landmark": "RainMid", "npc": "bubbles", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_03_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_04", "landmark": "RainForest", "npc": "tsktsk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_04_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_05", "landmark": "Rain", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_05_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_ap10_fetch_06", "landmark": "RainMid", "npc": "chuckle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_ap10_fetch_06_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_balance", "landmark": "DawnCave", "npc": "balance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_balance_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_carry", "landmark": "Dawn", "npc": "carry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_carry_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_chestpound", "landmark": "DawnCave", "npc": "chestpound", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_chestpound_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCome", "id": "world_quest_dawn_come", "landmark": "Dawn", "npc": "come", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_come_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_deepbreath", "landmark": "DawnCave", "npc": "deepbreath", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_deepbreath_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_dustoff", "landmark": "DawnCave", "npc": "dustoff", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_dustoff_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_loopdance", "landmark": "Dawn", "npc": "loopdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_loopdance_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteNoThanks", "id": "world_quest_dawn_nothanks", "landmark": "Dawn_Shrine", "npc": "nothanks", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_nothanks_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmotePoint", "id": "world_quest_dawn_point", "landmark": "Dawn", "npc": "point", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_point_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dawn_sass", "landmark": "Dawn", "npc": "sass", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dawn_sass_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dusk_acknowledge", "landmark": "DuskGraveyard", "npc": "acknowledge", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_acknowledge_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteStanceWide", "id": "world_quest_dusk_brave", "landmark": "DuskGraveyard", "npc": "brave", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_brave_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dusk_crabvoice", "landmark": "Dusk_CrabField", "npc": "crabvoice", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_crabvoice_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteFaint", "id": "world_quest_dusk_die", "landmark": "Dusk", "npc": "die", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_die_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dusk_dontgo", "landmark": "DuskGraveyard", "npc": "dontgo", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_dontgo_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteLookAround", "id": "world_quest_dusk_lookaround", "landmark": "DuskMid", "npc": "lookaround", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_lookaround_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_dusk_respect", "landmark": "DuskGraveyard", "npc": "respect", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_respect_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteSalute", "id": "world_quest_dusk_salute", "landmark": "Dusk_CrabField", "npc": "salute", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_salute_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteScared", "id": "world_quest_dusk_scared", "landmark": "Dusk", "npc": "scared", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_scared_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteStanceSneak", "id": "world_quest_dusk_sneaky", "landmark": "DuskGraveyard", "npc": "sneaky", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_dusk_sneaky_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_nature_vortex1", "landmark": "Prairie_Island", "npc": "timid", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_nature_vortex1_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_nature_vortex2", "landmark": "Prairie_Island", "npc": "bellyscratch", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_nature_vortex2_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_nature_vortex3", "landmark": "Prairie_Island", "npc": "gratitude", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_nature_vortex3_count"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteFloat", "id": "world_quest_night_float", "landmark": "Night2", "npc": "float", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_float_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteForce", "id": "world_quest_night_force", "landmark": "Night_ThirdFloor", "npc": "force", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_force_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmoteCallGhostManta", "id": "world_quest_night_ghost", "landmark": "Night_Shelter", "npc": "ghost", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_ghost_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_night_kungfu", "landmark": "Night2", "npc": "kungfu", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_kungfu_count"}, {"difficulty": "fair", "display_type": "count", "icon": "UiEmoteStanceSolemn", "id": "world_quest_night_love", "landmark": "Night_ThirdFloor", "npc": "love", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_love_count"}, {"difficulty": "hard", "display_type": "count", "icon": "UiEmotePray", "id": "world_quest_night_pray", "landmark": "Night", "npc": "pray", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_pray_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_night_shh", "landmark": "Night2", "npc": "shh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_shh_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_night_think", "landmark": "Night", "npc": "think", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_think_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_night_wise", "landmark": "NightArchive", "npc": "wise", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_night_wise_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_crabwalk", "landmark": "DuskOasis", "npc": "crabwalk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_crabwalk_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_doze", "landmark": "DuskOasis", "npc": "doze", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_doze_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_nod", "landmark": "DuskOasis", "npc": "nod", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_nod_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_playfight", "landmark": "DuskOasis", "npc": "playfight", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_playfight_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_scare", "landmark": "DuskOasis", "npc": "scare", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_scare_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_oasis_shrug", "landmark": "DuskOasis", "npc": "shrug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_oasis_shrug_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_bellyscratch", "landmark": "Prairie_Island", "npc": "bellyscratch", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_bellyscratch_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCallBird", "id": "world_quest_prairie_bird", "landmark": "Prairie_NestAndKeeper", "npc": "bird", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_bird_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteButterfly", "id": "world_quest_prairie_butterfly", "landmark": "<PERSON><PERSON><PERSON>F<PERSON>s", "npc": "butterfly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_butterfly_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_celebrate", "landmark": "<PERSON><PERSON><PERSON>F<PERSON>s", "npc": "celebrate", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_celebrate_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_dance", "landmark": "Prairie_NestAndKeeper", "npc": "dance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_dance_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_doublefive", "landmark": "Prairie_NestAndKeeper", "npc": "doublefive", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_doublefive_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_gratitude", "landmark": "Prairie_Island", "npc": "gratitude", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_gratitude_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_grumpy", "landmark": "Prairie_Island", "npc": "grumpy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_grumpy_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_jelly", "landmark": "Prairie_Island", "npc": "jelly", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_jelly_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmote<PERSON>augh", "id": "world_quest_prairie_laugh", "landmark": "Prairie_Village", "npc": "laugh", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_laugh_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_rally", "landmark": "Prairie_Island", "npc": "rally", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_rally_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_salutation", "landmark": "Prairie_Cave", "npc": "salutation", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_salutation_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteClap", "id": "world_quest_prairie_thumbsup", "landmark": "Prairie_Village", "npc": "thumbsup", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_thumbsup_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_prairie_timid", "landmark": "Prairie_Island", "npc": "timid", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_timid_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteWaveAnim", "id": "world_quest_prairie_wave", "landmark": "Prairie_Village", "npc": "wave", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_wave_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteSweat", "id": "world_quest_prairie_wipe", "landmark": "Prairie_Cave", "npc": "wipe", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_wipe_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteYawn", "id": "world_quest_prairie_yawn", "landmark": "Prairie_Village", "npc": "yawn", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_prairie_yawn_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_bubbles", "landmark": "RainMid", "npc": "bubbles", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_bubbles_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_chuckle", "landmark": "RainMid", "npc": "chuckle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_chuckle_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCold", "id": "world_quest_rain_cold", "landmark": "Rain", "npc": "cold", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_cold_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCry", "id": "world_quest_rain_cry", "landmark": "RainMid", "npc": "cry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_cry_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_eww", "landmark": "RainForest", "npc": "eww", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_eww_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_facepalm", "landmark": "Rain", "npc": "facepalm", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_facepalm_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_rain_hairtousle", "landmark": "Rain_Cave", "npc": "hairtousle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_hairtousle_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_rain_kiss", "landmark": "RainMid", "npc": "kiss", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_kiss_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_rain_lazycool", "landmark": "RainForest", "npc": "lazycool", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_lazycool_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_marching", "landmark": "RainForest", "npc": "marching", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_marching_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteStress", "id": "world_quest_rain_ohno", "landmark": "RainMid", "npc": "ohno", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_ohno_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteAngry", "id": "world_quest_rain_pout", "landmark": "RainForest", "npc": "pout", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_pout_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_rain_sarcastic", "landmark": "RainForest", "npc": "sarcastic", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_sarcastic_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteHideSeek", "id": "world_quest_rain_seek", "landmark": "RainForest", "npc": "seek", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_seek_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteShy", "id": "world_quest_rain_shy", "landmark": "RainForest", "npc": "shy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_shy_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteApologize", "id": "world_quest_rain_sorry", "landmark": "RainMid", "npc": "sorry", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_sorry_count"}, {"difficulty": "fair", "display_type": "count", "icon": "", "id": "world_quest_rain_tsktsk", "landmark": "Rain", "npc": "tsktsk", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_tsktsk_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCallFish", "id": "world_quest_rain_whale", "landmark": "RainMid", "npc": "whale", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_rain_whale_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteBackflip", "id": "world_quest_sunset_backflip", "landmark": "Sunset_Citadel", "npc": "backflip", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_backflip_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteBow", "id": "world_quest_sunset_bow", "landmark": "SunsetColosseum", "npc": "bow", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_bow_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCheer", "id": "world_quest_sunset_cheer", "landmark": "SunsetColosseum", "npc": "cheer", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_cheer_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteHandstand", "id": "world_quest_sunset_handstand", "landmark": "Sunset_Citadel", "npc": "handstand", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_handstand_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_joy", "landmark": "Sunset", "npc": "joy", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_joy_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_juggle", "landmark": "Sunset", "npc": "juggle", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_juggle_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteCallManta", "id": "world_quest_sunset_manta", "landmark": "Sunset_Citadel", "npc": "manta", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_manta_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteStanceCrossArm", "id": "world_quest_sunset_proud", "landmark": "SunsetColosseum", "npc": "proud", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_proud_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_sparkler", "landmark": "SunsetColosseum", "npc": "sparkler", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_sparkler_count"}, {"difficulty": "easy", "display_type": "count", "icon": "UiEmoteStanceHero", "id": "world_quest_sunset_strong", "landmark": "Sunset", "npc": "strong", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_strong_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_tripleaxel", "landmark": "Sunset", "npc": "tripleaxel", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_tripleaxel_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_village_bearhug", "landmark": "SunsetVillage", "npc": "bearhug", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_village_bearhug_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_village_peek", "landmark": "SunsetVillage", "npc": "peek", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_village_peek_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_village_showdance", "landmark": "SunsetVillage", "npc": "showdance", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_village_showdance_count"}, {"difficulty": "easy", "display_type": "count", "icon": "", "id": "world_quest_sunset_village_spintrick", "landmark": "SunsetVillage", "npc": "spintrick", "requires_questgiver": false, "stat_delta": 1.0, "stat_group_type": "collect", "stat_type": "world_quest_sunset_village_spintrick_count"}]