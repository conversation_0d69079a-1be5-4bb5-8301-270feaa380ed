<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		yklzrtUjNqyBKLu0dLMdBk1l9nk=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		qoQMSa4c2qJO5fUy4Aipbjc8sic=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		37/5nsYk1R/ccrxlNbMtZHJjMmk=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		fGHySbsIX67rRP0vXm1Pable3jU=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		Wqm8Bpkg60VapSxcXwSe/CfwEOI=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		1l6ZE8Nwe+WNPYrSXcTdwltNR9I=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		OrBZCsAiiyAr+mX68OpPgPWNC/M=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		JGoPeUY07wG712AHb+npRjggtm0=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		V4a/759AzrLYUq0I1OQ+rvrNhv8=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		XFQYv6SbDDpETMA9hQjDcHqDKFs=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		4KDOLmvVFTSyfB5iX6mpFLzgc/0=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		2zwtSjyBAKRVxg1yO7sTAhUpOG4=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		Dd7x0iHwxjtf0SOtOJiiyJIu+A4=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		RMYxkgzANqofmp0YAw5Hen2tyAU=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		ZCs4U6GAMbBZDybC6p5kQ1Et6b8=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		0uZ2bCqjfmjEExCnYMc9lBr8DiA=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		kscAH4YMVJQ7SngEgY/naC1Mch8=
		</data>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<data>
		sRTezJAu4gF7zgqjenU75ov94K0=
		</data>
		<key>Info.plist</key>
		<data>
		MgrFyV38DJgMsZfnn29h45YnGHI=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		LA+vaS5pGGsArUtwReDk1o/OfBw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			yklzrtUjNqyBKLu0dLMdBk1l9nk=
			</data>
			<key>hash2</key>
			<data>
			zzIpkeQoZVGIGvyRwku9fYXyLt4Nhh868/N8nk4/r0w=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			qoQMSa4c2qJO5fUy4Aipbjc8sic=
			</data>
			<key>hash2</key>
			<data>
			DeSdYbo/g509B9UFvWpVbS9tiy/1LNMFvMZLwHDgIek=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			37/5nsYk1R/ccrxlNbMtZHJjMmk=
			</data>
			<key>hash2</key>
			<data>
			hxegjofVbOUtRNpCXSE/ccNzsX96d6zMtHge0TT+YS8=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			fGHySbsIX67rRP0vXm1Pable3jU=
			</data>
			<key>hash2</key>
			<data>
			MvLEHfQ4OkewDikSfLtSsyOcjJn9qta5h1cGIg08gXQ=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Wqm8Bpkg60VapSxcXwSe/CfwEOI=
			</data>
			<key>hash2</key>
			<data>
			8XzajBv5nPpkR3tR1/WIfOew2vczf1STY99Q02Paa2E=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			1l6ZE8Nwe+WNPYrSXcTdwltNR9I=
			</data>
			<key>hash2</key>
			<data>
			G+Fi8pDHU15RpJfR67vD+kTBO8YxWrPo9BEAFxQ0fI4=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			OrBZCsAiiyAr+mX68OpPgPWNC/M=
			</data>
			<key>hash2</key>
			<data>
			H+nzSNqpaBPG85BRvA/z1IpxxZ8+SgXJC6thMXAYItI=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			JGoPeUY07wG712AHb+npRjggtm0=
			</data>
			<key>hash2</key>
			<data>
			cwo8Ou2+aT/gWEQgCWlboRpB/dylsKycrOcOYiegq3o=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			V4a/759AzrLYUq0I1OQ+rvrNhv8=
			</data>
			<key>hash2</key>
			<data>
			kyMnsIp1e3e1E3ehpFb11GZPESQHmlZFt45F7Hi/oqM=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			XFQYv6SbDDpETMA9hQjDcHqDKFs=
			</data>
			<key>hash2</key>
			<data>
			M8657HrhcIFgWWAnHrg6xvfp6uizUcy3efoU35ceVDg=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			4KDOLmvVFTSyfB5iX6mpFLzgc/0=
			</data>
			<key>hash2</key>
			<data>
			P04o0ZwtRbRGQtFC1ZcgbNEIsM/A0mpAx2tC6qU9ajs=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			2zwtSjyBAKRVxg1yO7sTAhUpOG4=
			</data>
			<key>hash2</key>
			<data>
			kgO7h4Qfa8BINPaBJOhNytCrHVX5U/7q9qYn+e23vFg=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Dd7x0iHwxjtf0SOtOJiiyJIu+A4=
			</data>
			<key>hash2</key>
			<data>
			wPYLjvKXZ51iWROBOB4OKvZbWhAYCIX1vv+PWowqAHA=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			RMYxkgzANqofmp0YAw5Hen2tyAU=
			</data>
			<key>hash2</key>
			<data>
			8w06IH/ffYJ1SnmyIDxfojFtKIxAtbHZzRWTbyXhqMA=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			ZCs4U6GAMbBZDybC6p5kQ1Et6b8=
			</data>
			<key>hash2</key>
			<data>
			Il0p2zCOFEGgAmZ2/F7CYeV+7D3TpU6Hvx5d86JFKxk=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			0uZ2bCqjfmjEExCnYMc9lBr8DiA=
			</data>
			<key>hash2</key>
			<data>
			6v2A8CPXRuH6+Ajq+KhGg+6eWwUJnDjelJUnqXVsANI=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			kscAH4YMVJQ7SngEgY/naC1Mch8=
			</data>
			<key>hash2</key>
			<data>
			WdQJmujSPL9nG5bxVSyyJwuXemPacBfeobJ5/aZ3qlo=
			</data>
		</dict>
		<key>GLFaceDetectSDK.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			sRTezJAu4gF7zgqjenU75ov94K0=
			</data>
			<key>hash2</key>
			<data>
			Qx2bl89msFzc9OEuI8KQ+86FQZnUORdX+N5oNbgRM5g=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			LA+vaS5pGGsArUtwReDk1o/OfBw=
			</data>
			<key>hash2</key>
			<data>
			yddc8gryRFOawTuYMJ2ZY00JHR4LktmAkp9ruQDxKMo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>