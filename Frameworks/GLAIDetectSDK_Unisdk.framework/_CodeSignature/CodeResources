<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		Drt1KB7eZ2RhWNi8vkxMKAOEbGM=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		vfBIpD7AMso2CCNHLUvRO8R3K9E=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		nwdgjEcW2bkpWOS/E3cEfdn2Df4=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		gd0gj/m/urpo7BmFX83VPLrwpe8=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		sDf0/07dpS1oQEUa8EbIiNIdsZo=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		kuv1Gnfk0r7BB8xTP8yTkYxVePY=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		mbBcOOKsFt8d/rdhG17g4T+uNWc=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		1TIYsCTwceRxKvOBobPlWikps1g=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		dWtTQR4OJrHimvfMjFIWMT/Ioh4=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		t6QiNaaa3WTkfuF2t/j7FNNaNYc=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		KOHfmMuvoPdJR2/54dRkdz5wiu0=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		+0i8opH9DDE19GUIodTAEcmwygU=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		agzFJXaxrBI2Pkir6bFnggVra4s=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		XtgWyVrwqda/wrTeHNpigKCuQPU=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		qwCm3SRBX5N/8NbqQb9GV5ZYwhU=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		tjSZSbCVcamPbzIHKQTv5FLFZAw=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		cg0P+i3QGzNYeCXDk32tblYjs34=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		7lJObCVqvtIg2OnCcFCaoDUBXF4=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		nqsxZWuB7BQocmabcsg19ehUC1Y=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		o6I5pqgSQ/O1/1vQ8F7xkmHnSIg=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		5NxNGncJtNcQSa5kJXLuSd+03cE=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		ZFt/rXgf9iaL8+rG262ttjuZ2KI=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		SB06RQom6/knKlydhEIuCbw3ZoM=
		</data>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<data>
		YwP6SBT/O7o91KfzpQv5hvRmQ6s=
		</data>
		<key>Info.plist</key>
		<data>
		+65ess3C1l6cLOhfZXFI1KVGlyo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Drt1KB7eZ2RhWNi8vkxMKAOEbGM=
			</data>
			<key>hash2</key>
			<data>
			ZbaeGY6nuYkiBCoWTAw4eki2i2GEk7E8VSkRDUHr86o=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			vfBIpD7AMso2CCNHLUvRO8R3K9E=
			</data>
			<key>hash2</key>
			<data>
			pIhxup930DMr3kGa7OLoJvyt9QNMZxtMXSE4E8XGxdU=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			nwdgjEcW2bkpWOS/E3cEfdn2Df4=
			</data>
			<key>hash2</key>
			<data>
			s1OfDVgztzsscpHN24SCOwlA+ssjSVtPcx6M/6y0YNM=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			gd0gj/m/urpo7BmFX83VPLrwpe8=
			</data>
			<key>hash2</key>
			<data>
			xDq7Y1wCGz04fNw6K4t6pKiCUbayESOoZfDYBIHKKJo=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			sDf0/07dpS1oQEUa8EbIiNIdsZo=
			</data>
			<key>hash2</key>
			<data>
			ZpJbU5ZcALs1uoVrwPIvGqTc4EAFE30NJ6e7waJ7iXA=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			kuv1Gnfk0r7BB8xTP8yTkYxVePY=
			</data>
			<key>hash2</key>
			<data>
			WgLASuVLxejDFPVvD3b/oeepOVDteS13r8SXtdPyO6A=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			mbBcOOKsFt8d/rdhG17g4T+uNWc=
			</data>
			<key>hash2</key>
			<data>
			2u4AZBP6TL3ciXYKfB0B2FgiyR0fGfasfqxdEZYwtdk=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			1TIYsCTwceRxKvOBobPlWikps1g=
			</data>
			<key>hash2</key>
			<data>
			s8ziRufd6dddD8n6T86fY1gWVuJRY9wwYUOPhbvb5xg=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			dWtTQR4OJrHimvfMjFIWMT/Ioh4=
			</data>
			<key>hash2</key>
			<data>
			FoDuhFdCBZ8juPXcw0V0I/ZTnWjZkdYvBK3+ACteXzw=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			t6QiNaaa3WTkfuF2t/j7FNNaNYc=
			</data>
			<key>hash2</key>
			<data>
			OKEaXb4hbeG4eHzgwX6IhbTo1B3zL8hasJmMDaiwuRk=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			KOHfmMuvoPdJR2/54dRkdz5wiu0=
			</data>
			<key>hash2</key>
			<data>
			uYSqWY4ZC+twALlXIhs4eiBOfIm4fMOLOpNkNszRU2o=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			+0i8opH9DDE19GUIodTAEcmwygU=
			</data>
			<key>hash2</key>
			<data>
			jcs/4YFPVIpZnnr9s2kL26DdC9K7XGJmNIaGImqW+1k=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			agzFJXaxrBI2Pkir6bFnggVra4s=
			</data>
			<key>hash2</key>
			<data>
			6jJOEgYJpEZ5qVch5BilxSnkKLPC+MhAYf1GC/t0rkc=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			XtgWyVrwqda/wrTeHNpigKCuQPU=
			</data>
			<key>hash2</key>
			<data>
			dQl4lctUXd7TOSyKfUgkvm+1n9d4rMn6bPQIGdVrD4U=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			qwCm3SRBX5N/8NbqQb9GV5ZYwhU=
			</data>
			<key>hash2</key>
			<data>
			caU7wAbK8QAn5ZPc1IBtJbMCg7d38k2tRTCSpnRBxP0=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			tjSZSbCVcamPbzIHKQTv5FLFZAw=
			</data>
			<key>hash2</key>
			<data>
			CTOKafst3zVBtBvHJR3NB5Olv6jOFdDloEwcuCATjXA=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			cg0P+i3QGzNYeCXDk32tblYjs34=
			</data>
			<key>hash2</key>
			<data>
			l2qAmRn/6rG0gg+2nhuAr9Mp1zaUJZ7XOuYE3MHexmQ=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7lJObCVqvtIg2OnCcFCaoDUBXF4=
			</data>
			<key>hash2</key>
			<data>
			aXTCuHZh5TsgiNwM8ouz9Youn0pT8i+afY189EC+YEU=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			nqsxZWuB7BQocmabcsg19ehUC1Y=
			</data>
			<key>hash2</key>
			<data>
			2Q7XPM9ME0BlA3dUIkM3mR8115eSLMmFohApq788Z0Y=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			o6I5pqgSQ/O1/1vQ8F7xkmHnSIg=
			</data>
			<key>hash2</key>
			<data>
			oGnNsP35JpeT9WIVx8TRvEHHFXX2pA11BnStqAkG41A=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			5NxNGncJtNcQSa5kJXLuSd+03cE=
			</data>
			<key>hash2</key>
			<data>
			cAJTu4Jud3O9B8XSUCaI2QXtvmvP5xCGXX0gtIJ+Byc=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			ZFt/rXgf9iaL8+rG262ttjuZ2KI=
			</data>
			<key>hash2</key>
			<data>
			j3u4NnHhBdazQf/ZAZhVHW4MPEqqej8jMk6hDkrXTC4=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			SB06RQom6/knKlydhEIuCbw3ZoM=
			</data>
			<key>hash2</key>
			<data>
			Rvt+XMU7iM0vOFOiJ4PhMaRIWnV44fKrQpk+3PEa1Nc=
			</data>
		</dict>
		<key>GLAIDetect.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			YwP6SBT/O7o91KfzpQv5hvRmQ6s=
			</data>
			<key>hash2</key>
			<data>
			GAfm+uNTFCTOvI6UlxhjIRMrjXJm79gKMZWx2+S0j2k=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>