<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AoeSDKBranchName</key>
	<string>0.6.8-aifacesuit</string>
	<key>AoeSDKCommitId</key>
	<string>cdb0262a</string>
	<key>BuildMachineOSBuild</key>
	<string>21G83</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>AIFaceSuite</string>
	<key>CFBundleIdentifier</key>
	<string>aoe.AIFaceSuit</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>AIFaceSuite</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>iPhoneOS</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>19F64</string>
	<key>DTPlatformName</key>
	<string>iphoneos</string>
	<key>DTPlatformVersion</key>
	<string>15.5</string>
	<key>DTSDKBuild</key>
	<string>19F64</string>
	<key>DTSDKName</key>
	<string>iphoneos15.5</string>
	<key>DTXcode</key>
	<string>1341</string>
	<key>DTXcodeBuild</key>
	<string>13F100</string>
	<key>MinimumOSVersion</key>
	<string>8.0</string>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
</dict>
</plist>
