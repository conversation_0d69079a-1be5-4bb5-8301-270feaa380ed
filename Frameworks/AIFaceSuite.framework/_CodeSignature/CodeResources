<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		YkNK3a3aanSUS5tNbkrIk5Clfb8=
		</data>
		<key>RFB_Medium_ns16_nb5_nl106_test_1_sim_fp16.bin</key>
		<data>
		4g5tCH22np7VZwPp+dwY77Hj2/Q=
		</data>
		<key>RFB_Medium_ns16_nb5_nl106_test_1_sim_fp16.param.bin</key>
		<data>
		aETsjviZ0l6B4SATCjtz1iouNRU=
		</data>
		<key>rfb320_1m_6class_opt.bin</key>
		<data>
		AhjRF9DqCLjzbZvwKB7pzJB1MgQ=
		</data>
		<key>rfb320_1m_6class_opt.param.bin</key>
		<data>
		q9kaR5mg6vVI9zZ19ZQc7rd4Aec=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>RFB_Medium_ns16_nb5_nl106_test_1_sim_fp16.bin</key>
		<dict>
			<key>hash</key>
			<data>
			4g5tCH22np7VZwPp+dwY77Hj2/Q=
			</data>
			<key>hash2</key>
			<data>
			9pnZ52uJpHseKzdSNVXzlo4jhd2tLA5f84n+KrU8gfo=
			</data>
		</dict>
		<key>RFB_Medium_ns16_nb5_nl106_test_1_sim_fp16.param.bin</key>
		<dict>
			<key>hash</key>
			<data>
			aETsjviZ0l6B4SATCjtz1iouNRU=
			</data>
			<key>hash2</key>
			<data>
			THfhLq2uUtt4GGz4n7vHTgragqYz4rRcsBCouboer9I=
			</data>
		</dict>
		<key>rfb320_1m_6class_opt.bin</key>
		<dict>
			<key>hash</key>
			<data>
			AhjRF9DqCLjzbZvwKB7pzJB1MgQ=
			</data>
			<key>hash2</key>
			<data>
			jCAHjTTczEt+wfQCKft/Leu/rWwY0BmWEfdiHWkFMyI=
			</data>
		</dict>
		<key>rfb320_1m_6class_opt.param.bin</key>
		<dict>
			<key>hash</key>
			<data>
			q9kaR5mg6vVI9zZ19ZQc7rd4Aec=
			</data>
			<key>hash2</key>
			<data>
			N0G8V7iGAO9+oBZV1OX82Oz19a4lT9n3IcG+ofeBT5c=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>