function NeteaseMpay_loadURL(url) {
    var iFrame;
    iFrame = document.createElement("iframe");
    iFrame.setAttribute("src", url);
    iFrame.setAttribute("style", "display:none;");
    iFrame.setAttribute("height", "0px");
    iFrame.setAttribute("width", "0px");
    iFrame.setAttribute("frameborder", "0");
    document.body.appendChild(iFrame);
    iFrame.parentNode.removeChild(iFrame);
    iFrame = null;
}

function connectWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
    if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'https://__bridge_loaded__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function() { document.documentElement.removeChild(WVJBIframe) }, 0)
}

if (typeof(NeteaseMpayJSBridge) == "undefined") {
    var NeteaseMpayJSBridge = {};
    connectWebViewJavascriptBridge(function(bridge) {

        NeteaseMpayJSBridge.RelatedAccount = {
            // 验证关联手机结果回调
            'onVerifyRelatedMoible': function(res) {
                bridge.callHandler('NeteaseMpayJSBridge.RelatedAccount.onVerifyRelatedMoible', res);
            },

            // 跳转到移动账号换号的界面
            'jumpToMobileChangePage': function() {
                bridge.callHandler('NeteaseMpayJSBridge.RelatedAccount.jumpToMobileChangePage');
            }
        };

        NeteaseMpayJSBridge.Common = {
            // 关闭窗口
            'closeWindow': function() {
                bridge.callHandler('NeteaseMpayJSBridge.Common.closeWindow');
            },
            // 登录游戏
            'onUserLogin': function(user) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onUserLogin', user);
            },
            // 登出游戏
            'onLogout': function(code) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onLogout', code);
            },
            // 修改导航栏标题；
            'changeNavigationTitle': function(title) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.changeNavigationTitle', title);
            },
            // 二次验证
            'onVerify': function(res) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onVerify', res);
            },
			// 二次验证失败
			'onVerifyFailure': function(res) {
				bridge.callHandler('NeteaseMpayJSBridge.Common.onVerifyFailure', res);
			},
            // 与安卓统一，方便服务端处理
            'onError': function(res) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onError', res);
            },
              
            // 手动实名页面已实名状态
            'onRealnameVerify': function(res) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onRealnameVerify', res);
            },

            // 错误提示
            'toast': function(msg) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.toast', msg);
            },

            'alert': function(msg) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.alert', msg);
            },

            // 通过Safari打开url
            'setUrlPrefixForNativeBrowser': function(urlPrefixList) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.setUrlPrefixForNativeBrowser', urlPrefixList);
            },
                                           
            // 使用原生的浏览器打开某个url
            'openLinkInNativeBrowser': function(url) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.openLinkInNativeBrowser', url);
            },

            // 复制文本
            'saveToClipboard': function(content) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.saveToClipboard', content);
            },

            // 保存图片
            'saveImage': function(url) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.saveImage', url);
            },

            // 获取配置信息
            'getConfig': function(params) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.getConfig', null, function(response) {
                    params.success(response);
                });
            },
            'onYiDunAliveDetect': function(params, callback) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onYiDunAliveDetect', params, callback);
            },
            'onGLFaceDetect': function(params, callback) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.onGLFaceDetect', params, callback);
            },
            'clientLog': function(log) {
                bridge.callHandler('NeteaseMpayJSBridge.Common.clientLog', log);
            },
        };
        
        // 移动账号安全中心的API
        NeteaseMpayJSBridge.MobileCenter = {
            'onUserLogin': function(user) {
                bridge.callHandler('NeteaseMpayJSBridge.MobileCenter.onUserLogin', user);
            }
        };

        // 发送事件
        var evt = document.createEvent("Events");
        evt.initEvent("NeteaseMpayJSBridgeReady", true, true);
        document.dispatchEvent(evt);
    });
}
