{"CommitMessage": "", "Editor": "liuhuanan", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1670", "FullTextBase64Url": "http://protocol.unisdk.netease.com/release/r1670", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/release/r1670.png", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1670.thanks", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1670.update", "FullTextUrl": "http://protocol.unisdk.netease.com/release/r1670.png", "Hash": "27d73227b8335a6c8c627b7a3901152c", "HashMethod": "MD5", "HashThanks": "d41d8cd98f00b204e9800998ecf8427e", "HashUpdate": "09e0b0a8698c3363ae48a99c78248778", "Id": 1670, "IsHidden": false, "IsMinorChange": true, "ParentId": 1669, "PrevMajorChangeId": 811, "Status": 103, "SubProtocol": [], "TextAlign": 1, "UpdatedAt": "2022-11-17T14:40:50.37620273+08:00", "VersionId": {"CreatedAt": "2021-07-04T20:51:20+08:00", "Creator": "wshn0640", "DisplayName": "网易游戏隐私政策", "Id": 90, "IsDefault": 0, "Status": 201, "VersionName": "网易游戏隐私政策(正式版)"}}