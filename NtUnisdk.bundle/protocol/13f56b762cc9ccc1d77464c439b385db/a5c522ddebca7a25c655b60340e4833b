{"CommitMessage": "", "Editor": "liuhuanan", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1636", "FullTextBase64Url": "http://protocol.unisdk.netease.com/release/r1636", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/release/r1636.png", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1636.thanks", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1636.update", "FullTextUrl": "http://protocol.unisdk.netease.com/release/r1636.png", "Hash": "74492b8506d42d0ed20ea1383ad188be", "HashMethod": "MD5", "HashThanks": "d41d8cd98f00b204e9800998ecf8427e", "HashUpdate": "d41d8cd98f00b204e9800998ecf8427e", "Id": 1636, "IsHidden": false, "IsMinorChange": true, "ParentId": 1634, "PrevMajorChangeId": 80, "Status": 103, "SubProtocol": [], "TextAlign": 1, "UpdatedAt": "2022-11-08T14:53:00.546870461+08:00", "VersionId": {"CreatedAt": "2016-03-09T07:08:43+08:00", "Creator": "gzzhangchengfang", "DisplayName": "网易游戏使用许可及服务协议", "Id": 5, "IsDefault": 1, "Status": 201, "VersionName": "网易游戏使用许可及服务协议（2022年11月7日版）"}}