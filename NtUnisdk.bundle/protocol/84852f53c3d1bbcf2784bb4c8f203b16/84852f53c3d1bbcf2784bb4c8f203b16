{"Id": 3536, "IsMinorChange": false, "ParentId": 3526, "PrevMajorChangeId": 3526, "UpdatedAt": "2023-08-25 16:49:28", "Status": 103, "CommitMessage": "兼容安卓native显示（跟随子协议106-3521发布）", "IsHidden": false, "UiStyle": 0, "TextAlign": 1, "Editor": "wshn0640", "i18n": {"unisdk_protocol_confirm": "确认", "unisdk_protocol_reject": "拒绝", "unisdk_protocol_accept": "接受", "unisdk_protocol_reject_confirm_msg": "是否确认拒绝协议？拒绝后将无法继续游戏", "unisdk_protocol_reject_confirm_back": "返回", "archive": "archive", "comparison": "comparison"}, "VersionId": {"IsDefault": 0, "Status": 201, "CreatedAt": "2021-07-06 04:48:34", "Creator": "wshn0640", "DisplayName": "用户协议与隐私政策", "Id": 89, "VersionName": "用户协议与隐私政策(正式版)"}, "SubProtocol": [{"ProtocolName": "网易游戏隐私政策", "ProtocolUrl": "https://protocol.unisdk.netease.com/api/template/v90/latest.json", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v90.tw.json"}, {"ProtocolName": "网易游戏使用许可及服务协议", "ProtocolUrl": "https://protocol.unisdk.netease.com/api/template/v5/latest.json", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v5.tw.json"}, {"ProtocolName": "网易游戏儿童个人信息保护规则及监护人须知", "ProtocolUrl": "https://protocol.unisdk.netease.com/api/template/v106/latest.json", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v106.tw.json"}], "HashMethod": "MD5", "Hash": "1a64ee05745d59d3d7c11cbd552ad8be", "HashThanks": "1a64ee05745d59d3d7c11cbd552ad8be", "HashUpdate": "1a64ee05745d59d3d7c11cbd552ad8be", "FullTextUrl": "http://protocol.unisdk.netease.com/api/template/v89/r1180.png", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/api/template/v89/r1180.png", "FullTextBase64Url": "http://protocol.unisdk.netease.com/api/template/v89/r1180.b64", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v89/r1180.b64", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v89/r1180.b64", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v89/r1180.b64", "AddParamsHosts": ["unisdk.update.netease.com", "protocol.unisdk.netease.com"]}