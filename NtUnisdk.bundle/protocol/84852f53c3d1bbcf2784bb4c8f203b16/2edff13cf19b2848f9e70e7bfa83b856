{"Id": 3520, "IsMinorChange": false, "ParentId": 3421, "PrevMajorChangeId": 811, "UpdatedAt": "2023-08-25 16:01:02", "Status": 103, "CommitMessage": "", "IsHidden": false, "UiStyle": 0, "TextAlign": 1, "Editor": "wshn0640", "VersionId": {"IsDefault": 0, "Status": 201, "CreatedAt": "2021-07-04 20:51:20", "Creator": "wshn0640", "DisplayName": "网易游戏隐私政策", "Id": 90, "VersionName": "网易游戏隐私政策(正式版)"}, "SubProtocol": [], "HashMethod": "MD5", "Hash": "c4e56df7afda04228f1e19b624b75ed4", "HashThanks": "c4e56df7afda04228f1e19b624b75ed4", "HashUpdate": "c4e56df7afda04228f1e19b624b75ed4", "FullTextUrl": "http://protocol.unisdk.netease.com/api/template/v90/r2078.png", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/api/template/v90/r2078.png", "FullTextBase64Url": "http://protocol.unisdk.netease.com/api/template/v90/r2078.b64", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v90/r2078.b64", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v90/r2078.b64", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v90/r2078.b64", "AddParamsHosts": ["unisdk.update.netease.com", "protocol.unisdk.netease.com"]}