{"Id": 3521, "IsMinorChange": false, "ParentId": 3428, "PrevMajorChangeId": 799, "UpdatedAt": "2023-08-25 16:02:25", "Status": 103, "CommitMessage": "", "IsHidden": false, "UiStyle": 0, "TextAlign": 1, "Editor": "wshn0640", "VersionId": {"IsDefault": 0, "Status": 201, "CreatedAt": "2021-11-01 08:39:21", "Creator": "wshn0640", "DisplayName": "网易游戏儿童个人信息保护规则及监护人须知", "Id": 106, "VersionName": "网易游戏儿童个人信息保护规则及监护人须知"}, "SubProtocol": [], "HashMethod": "MD5", "Hash": "2fb82a7215866d31ae9b1711eade75aa", "HashThanks": "2fb82a7215866d31ae9b1711eade75aa", "HashUpdate": "2fb82a7215866d31ae9b1711eade75aa", "FullTextUrl": "http://protocol.unisdk.netease.com/api/template/v106/r2076.png", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/api/template/v106/r2076.png", "FullTextBase64Url": "http://protocol.unisdk.netease.com/api/template/v106/r2076.b64", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v106/r2076.b64", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v106/r2076.b64", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v106/r2076.b64", "AddParamsHosts": ["unisdk.update.netease.com", "protocol.unisdk.netease.com"]}