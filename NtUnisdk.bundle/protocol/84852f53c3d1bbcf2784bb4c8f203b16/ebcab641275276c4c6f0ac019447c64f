{"Id": 3403, "IsMinorChange": true, "ParentId": 3318, "PrevMajorChangeId": 80, "UpdatedAt": "2023-08-14 14:27:58", "Status": 103, "CommitMessage": "[replace]帐号->账号", "IsHidden": false, "UiStyle": 0, "TextAlign": 1, "Editor": "system", "VersionId": {"IsDefault": 0, "Status": 201, "CreatedAt": "2016-03-09 07:08:43", "Creator": "gzzhangchengfang", "DisplayName": "网易游戏使用许可及服务协议", "Id": 5, "VersionName": "网易游戏使用许可及服务协议（2022年11月7日版）"}, "SubProtocol": [], "HashMethod": "MD5", "Hash": "28bb6300f737f4aca9da27d0a9c71a53", "HashThanks": "28bb6300f737f4aca9da27d0a9c71a53", "HashUpdate": "28bb6300f737f4aca9da27d0a9c71a53", "FullTextUrl": "http://protocol.unisdk.netease.com/api/template/v5/r1719.png", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/api/template/v5/r1719.png", "FullTextBase64Url": "http://protocol.unisdk.netease.com/api/template/v5/r1719.b64", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v5/r1719.b64", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v5/r1719.b64", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/api/template/v5/r1719.b64", "AddParamsHosts": ["unisdk.update.netease.com", "protocol.unisdk.netease.com"]}