{"CommitMessage": "", "Editor": "liuhuanan", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1652", "FullTextBase64Url": "http://protocol.unisdk.netease.com/release/r1652", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/release/r1652.png", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1652.thanks", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1652.update", "FullTextUrl": "http://protocol.unisdk.netease.com/release/r1652.png", "Hash": "416e14d6a27ebf905642f5dae260776e", "HashMethod": "MD5", "HashThanks": "d41d8cd98f00b204e9800998ecf8427e", "HashUpdate": "d41d8cd98f00b204e9800998ecf8427e", "Id": 1652, "IsHidden": false, "IsMinorChange": true, "ParentId": 1632, "PrevMajorChangeId": 799, "Status": 103, "SubProtocol": [], "TextAlign": 1, "UpdatedAt": "2022-11-08T14:53:54.119498031+08:00", "VersionId": {"CreatedAt": "2021-11-01T08:39:21+08:00", "Creator": "wshn0640", "DisplayName": "网易游戏儿童个人信息保护规则及监护人须知", "Id": 106, "IsDefault": 0, "Status": 201, "VersionName": "网易游戏儿童个人信息保护规则及监护人须知"}}