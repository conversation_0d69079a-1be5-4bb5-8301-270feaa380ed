{"CommitMessage": "", "Editor": "liuhuanan", "FullTextBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1675", "FullTextBase64Url": "http://protocol.unisdk.netease.com/release/r1675", "FullTextHttpsUrl": "https://protocol.unisdk.netease.com/release/r1675.png", "FullTextThanksBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1675.thanks", "FullTextUpdateBase64HttpsUrl": "https://protocol.unisdk.netease.com/release/r1675.update", "FullTextUrl": "http://protocol.unisdk.netease.com/release/r1675.png", "Hash": "7a8cc9409d8ebe8471ded37185174544", "HashMethod": "MD5", "HashThanks": "7a8cc9409d8ebe8471ded37185174544", "HashUpdate": "7a8cc9409d8ebe8471ded37185174544", "Id": 1675, "IsHidden": false, "IsMinorChange": true, "ParentId": 1668, "PrevMajorChangeId": 1664, "Status": 103, "SubProtocol": [{"ProtocolName": "网易游戏使用许可及服务协议", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v5.tw.json", "ProtocolUrl": "https://protocol.unisdk.netease.com/release/latest_v5.json"}, {"ProtocolName": "网易集团隐私政策", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v90.tw.json", "ProtocolUrl": "https://protocol.unisdk.netease.com/release/latest_v90.json"}, {"ProtocolName": "网易游戏儿童个人信息保护规则及监护人须知", "ProtocolTwUrl": "https://protocol.unisdk.easebar.com/release/latest_v106.tw.json", "ProtocolUrl": "https://protocol.unisdk.netease.com/release/latest_v106.json"}], "TextAlign": 1, "UpdatedAt": "2022-11-17T14:41:15.497631795+08:00", "VersionId": {"CreatedAt": "2021-07-06T04:48:34+08:00", "Creator": "wshn0640", "DisplayName": "用户协议与隐私政策", "Id": 89, "IsDefault": 0, "Status": 201, "VersionName": "用户协议与隐私政策(正式版)"}}