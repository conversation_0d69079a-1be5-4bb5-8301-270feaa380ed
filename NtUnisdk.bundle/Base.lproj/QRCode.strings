/*
  QRCode.strings
  Demo_netease2

  Created by game-netease on 2017/6/28.
  Copyright © 2017年 game-netease. All rights reserved.
*/

"scanning" = "Scanning";
"scanning_en" = "Scanning";
"scanning_zh-Hans" = "扫一扫";
"scanning_zh-Hant" = "掃一掃";

"album" = "Album";
"album_en" = "Album";
"album_zh-Hans" = "相册";
"album_zh-Hant" = "相簿";

"tipsForWindow" = "Window focus on QRCode";
"tipsForWindow_en" = "Window focus on QRCode";
"tipsForWindow_zh-Hans" = "将取景框对准二维码扫描";
"tipsForWindow_zh-Hant" = "將取景框對準二維碼掃描";

"torchNormal" = "Torch normal";
"torchNormal_en" = "Torch normal";
"torchNormal_zh-Hans" = "打开手电筒";
"torchNormal_zh-Hant" = "打開手電筒";

"torchSelected" = "Torch selected";
"torchSelected_en" = "Torch selected";
"torchSelected_zh-Hans" = "关闭手电筒";
"torchSelected_zh-Hant" = "關閉手電筒";

"alertTitle" = "Enable Quick Recognition";
"alertTitle_en" = "Enable Quick Recognition";
"alertTitle_zh-Hans" = "开启快捷识别";
"alertTitle_zh-Hant" = "開啟快捷識別";

"recentQRCodeImage" = "Recent QRCode image";
"recentQRCodeImage_en" = "Recent QRCode image";
"recentQRCodeImage_zh-Hans" = "近期扫描的二维码图片";
"recentQRCodeImage_zh-Hant" = "近期掃描的二維碼圖片";

"alertSubTitle" = "Allow access all photos in Settings -> Privacy -> Album to enable quick identify recent QRCode images";
"alertSubTitle_en" = "Allow access all photos in Settings -> Privacy -> Album to enable quickly identify recent QRCode images";
"alertSubTitle_zh-Hans" = "前往设置-隐私-照片中授权当前APP所有照片访问权限可以在游戏中在开启快捷识别近期的二维码图片功能";
"alertSubTitle_zh-Hant" = "前往設置-隱私-照片中授權當前APP所有照片訪問權限可以在遊戲中在開啟快捷識別近期的二維碼圖片功能";

"highlightAlertSubTitle" = "Settings -> Privacy -> Album";
"highlightAlertSubTitle_en" = "Settings -> Privacy -> Album";
"highlightAlertSubTitle_zh-Hans" = "设置-隐私-照片";
"highlightAlertSubTitle_zh-Hant" = "設置-隱私-照片";

"alertOpen" = "Go to open";
"alertOpen_en" = "Go to open";
"alertOpen_zh-Hans" = "前往开启";
"alertOpen_zh-Hant" = "前往開啟";

"alertClose" = "Never pop up again";
"alertClose_en" = "Never pop up again";
"alertClose_zh-Hans" = "以后不再弹出";
"alertClose_zh-Hant" = "以後不再彈出";
